<!DOCTYPE html>
<!--
Template Name: Midone HTML Admin Template
Author: Left4code
Website: http://www.left4code.com/
Contact: muham<PERSON><PERSON><PERSON>@left4code.com
Purchase: https://themeforest.net/user/left4code/portfolio
Renew Support: https://themeforest.net/user/left4code/portfolio
License: You must have a valid license purchased only from themeforest(the above link) in order to legally use the theme for your project.
-->
<html lang="en">
    <!-- BEGIN: Head -->
    <head>
        <meta charset="utf-8">
        <link href="http://localhost/dist/images/logo.svg" rel="shortcut icon">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="Midone admin is super flexible, powerful, clean & modern responsive tailwind admin template with unlimited possibilities.">
        <meta name="keywords" content="admin template, Midone admin template, dashboard template, flat admin template, responsive admin template, web app">
        <meta name="author" content="LEFT4CODE">
        <title>Attributions - Midone - Tailwind HTML Admin Template</title>
        <!-- BEGIN: CSS Assets-->
        <link rel="stylesheet" href="dist/css/app.css" />
        <!-- END: CSS Assets-->
    </head>
    <!-- END: Head -->
    <body>
        <div class="container grid grid-cols-12 -my-3 min-h-screen relative z-10">
            <div class="col-span-3 bg-gray-800 py-10">
                <a href="" class="intro-x flex items-center pl-5 mb-8">
                    <img alt="Midone Tailwind HTML Admin Template" class="w-6" src="dist/images/logo.svg">
                    <span class="hidden xl:block text-2xl ml-3"> Mid<span class="font-medium">one</span> Docs </span>
                </a>
                <a href="index.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Getting Started</a> <a href="file-structure.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">File Structure</a> <a href="installation.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Installation</a> <a href="style-customization.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Style Customization</a> <a href="javascript.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Javascript</a> <a href="attributions.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out font-medium bg-gray-900">Attributions</a> <a href="support.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Support</a> <a href="changelog.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Changelog</a> 
            </div>
            <div class="col-span-9 border-l border-gray-800 bg-gray-900 py-10 px-10">
                <h1 class="intro-y text-2xl font-medium pb-8 border-b border-gray-800">
                    Attributions
                </h1>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <h2 class="intro-y text-xl font-medium mb-5 border-b border-gray-800">
                        Images
                    </h2>
                    <table class="intro-y w-full">
                        <tr>
                            <td class="border border-gray-700 px-4 py-2 font-medium">Source</td>
                            <td class="border border-gray-700 px-4 py-2 font-medium">Url</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Unsplash</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://unsplash.com/">https://unsplash.com/</a></td>
                        </tr>
                    </table>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-10">
                    <h2 class="intro-y text-xl font-medium mb-5 border-b border-gray-800">
                        Plugins
                    </h2>
                    <table class="intro-y w-full">
                        <tr>
                            <td class="border border-gray-700 px-4 py-2 font-medium">Source</td>
                            <td class="border border-gray-700 px-4 py-2 font-medium">Url</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">CKEditor</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://ckeditor.com/">https://ckeditor.com/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Chartjs</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://www.chartjs.org/">https://www.chartjs.org/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">DayJs</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://github.com/iamkun/dayjs">https://github.com/iamkun/dayjs</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">DropzoneJs</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://www.dropzonejs.com/">https://www.dropzonejs.com/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Feather Icons</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://feathericons.com/">https://feathericons.com/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">HighlightJs</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://highlightjs.org/">https://highlightjs.org/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">JS Beautify</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://github.com/beautify-web/js-beautify">https://github.com/beautify-web/js-beautify</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Litepicker</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://wakirin.github.io/Litepicker/">https://wakirin.github.io/Litepicker/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">PristineJs</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="http://pristine.js.org/">http://pristine.js.org/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Tabulator</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="http://tabulator.info/">http://tabulator.info/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Tail Select</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://github.pytes.net/tail.select/">https://github.pytes.net/tail.select/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Tiny Slider</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://github.com/ganlanyuan/tiny-slider">https://github.com/ganlanyuan/tiny-slider</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">TippyJs</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://atomiks.github.io/tippyjs/">https://atomiks.github.io/tippyjs/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">ToastifyJs</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://github.com/apvarun/toastify-js">https://github.com/apvarun/toastify-js</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Velocity Animate</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="http://velocityjs.org/">http://velocityjs.org/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">SheetJs</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://sheetjs.com/">https://sheetjs.com/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Zoom Vanilla</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://github.com/spinningarrow/zoom-vanilla.js/">https://github.com/spinningarrow/zoom-vanilla.js/</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Axios</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://github.com/axios/axios">https://github.com/axios/axios</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">SVG Loaders</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://github.com/SamHerbert/SVG-Loaders">https://github.com/SamHerbert/SVG-Loaders</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Laravel Mix</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://laravel-mix.com/">https://laravel-mix.com/</a></td>
                        </tr>
                    </table>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-10">
                    <h2 class="intro-y text-xl font-medium mb-5 border-b border-gray-800">
                        Special Thanks
                    </h2>
                    <table class="intro-y w-full">
                        <tr>
                            <td class="border border-gray-700 px-4 py-2 font-medium">Source</td>
                            <td class="border border-gray-700 px-4 py-2 font-medium">Url</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">Cash</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://github.com/fabiospampinato/cash">https://github.com/fabiospampinato/cash</a></td>
                        </tr>
                        <tr>
                            <td class="border border-gray-700 px-4 py-2">TailwindCSS</td>
                            <td class="border border-gray-700 px-4 py-2"><a class="hover:text-white" target="blank" href="https://tailwindcss.com/">https://tailwindcss.com/</a></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <!-- BEGIN: JS Assets-->
        <script src="dist/js/app.js"></script>
        <!-- END: JS Assets-->
    </body>
</html>