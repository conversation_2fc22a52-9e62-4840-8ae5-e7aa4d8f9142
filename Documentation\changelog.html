<!DOCTYPE html>
<!--
Template Name: Midone HTML Admin Template
Author: Left4code
Website: http://www.left4code.com/
Contact: muham<PERSON><PERSON><PERSON>@left4code.com
Purchase: https://themeforest.net/user/left4code/portfolio
Renew Support: https://themeforest.net/user/left4code/portfolio
License: You must have a valid license purchased only from themeforest(the above link) in order to legally use the theme for your project.
-->
<html lang="en">
    <!-- BEGIN: Head -->
    <head>
        <meta charset="utf-8">
        <link href="http://localhost/dist/images/logo.svg" rel="shortcut icon">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="Midone admin is super flexible, powerful, clean & modern responsive tailwind admin template with unlimited possibilities.">
        <meta name="keywords" content="admin template, Midone admin template, dashboard template, flat admin template, responsive admin template, web app">
        <meta name="author" content="LEFT4CODE">
        <title>Installation - Midone - Tailwind HTML Admin Template</title>
        <!-- BEGIN: CSS Assets-->
        <link rel="stylesheet" href="dist/css/app.css" />
        <!-- END: CSS Assets-->
    </head>
    <!-- END: Head -->
    <body>
        <div class="container grid grid-cols-12 -my-3 min-h-screen relative z-10">
            <div class="col-span-3 bg-gray-800 py-10">
                <a href="" class="intro-x flex items-center pl-5 mb-8">
                    <img alt="Midone Tailwind HTML Admin Template" class="w-6" src="dist/images/logo.svg">
                    <span class="hidden xl:block text-2xl ml-3"> Mid<span class="font-medium">one</span> Docs </span>
                </a>
                <a href="index.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Getting Started</a> <a href="file-structure.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">File Structure</a> <a href="installation.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Installation</a> <a href="style-customization.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Style Customization</a> <a href="javascript.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Javascript</a> <a href="attributions.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Attributions</a> <a href="support.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Support</a> <a href="changelog.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out font-medium bg-gray-900">Changelog</a> 
            </div>
            <div class="col-span-9 border-l border-gray-800 bg-gray-900 py-10 px-10">
                <h1 class="intro-y text-2xl font-medium pb-8 mb-10 border-b border-gray-800">
                    Changelog
                </h1>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 2.0.2</span> - 7 September, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Rework dropdown component placement by using popper.js</li>
                            <li>Add new programmatically show/hide methods for dropdown component</li>
                            <li>Add new data-dismiss property to dismiss the dropdown box</li>
                            <li>Change cash-dom global identifier from $() to cd()</li>
                            <li>Bug fixes and performance improvements</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 2.0.1</span> - 25 August, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Fix responsive issue on tabulator page</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 2.0.0</span> - 21 August, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Dropping jQuery as a required dependency which effects the replacement of all jQuery plugins to the VanillaJs plugins</li>
                            <li>
                                List of replaced plugins: 
                                <ul class="list-disc ml-6">
                                    <li>jQuery to Cash-DOM (For DOM manipulation), VelocityJs (For Animations), Axios (For client HTTP request)</li>
                                    <li>Datatables to Tabulator Table</li>
                                    <li>Daterangepicker to Litepicker</li>
                                    <li>Driftzoom to Zoom Vanilla</li>
                                    <li>Jquery Toast to Toastify</li>
                                    <li>Momentjs to Dayjs</li>
                                    <li>Select2 to Tailselect</li>
                                    <li>Slick Carousel to Tiny Slider</li>
                                    <li>Summernote to CKEditor</li>
                                    <li>Tooltipster to TippyJs</li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.1.3</span> - 13 July, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Add new dark mode version</li>
                            <li>Add new "on show" & "on hide" modal event that triggered when the modal showed or closed</li>
                            <li>Add new Tailwind "dark" utility to simply apply dark mode styling</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.1.2</span> - 13 June, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Update DropzoneJS SASS file usage</li>
                            <li>Fix compiling issues</li>
                            <li>Make Feather Icons accessible globally</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.1.1</span> - 18 May, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Expose jQuery & plugins globally.</li>
                            <li>Bug fixing & performance improvements.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.1.0</span> - 04 May, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Added new modal variants.</li>
                            <li>Added new update profile page.</li>
                            <li>Added new change password page.</li>
                            <li>Updated documentation.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.10</span> - 04 May, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Fix missing summernote fonts on production build.</li>
                            <li>Fix missing slick carousel ajax.gif on production build.</li>
                            <li>Updated documentation.</li>
                            <li>Added new data-dismiss property to close modal.</li>
                            <li>Added new post news Application.</li>
                            <li>Minor adjustment on wysiwyg editor styles.</li>
                            <li>Upgrade tailwind version to v1.4.0.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.9</span> - 27 Apr, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Refactor google map style & icons.</li>
                            <li>Minor adjustment on POS page.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.8</span> - 27 Apr, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Added new POS Application.</li>
                            <li>Added new CRUD Data List & Form Application.</li>
                            <li>Added new Toast component.</li>
                            <li>Added new modal programmatic API.</li>
                            <li>Minor adjustment on inbox, file manager & datatable page.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.7</span> - 22 Apr, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Bug fixing and responsive improvements.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.6</span> - 22 Apr, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Refactoring dashboard page UI design.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.5</span> - 16 Apr, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Added new loading icon page.</li>
                            <li>Added new loading state button components.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.4</span> - 16 Apr, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Fix copy source code issue on every components page.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.3</span> - 15 Apr, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Fix issue on dropdown menu & mobile notifications.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.2</span> - 15 Apr, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>Remove <span class="text-yellow-500">.container</span> to every pages to improve data view & user experience at wide screen.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.1</span> - 14 Apr, 2020</div>
                    <div class="mt-3">
                        <ul class="list-disc ml-6 mt-3">
                            <li>New pages were added including applications & components.</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-y p-8 rounded-md bg-gray-800 mt-16">
                    <div class=""><span class="text-yellow-500">Version 1.0.0</span> - 12 Apr, 2020</div>
                    <div class="mt-3">
                        <span class="px-2 py-1 text-xs rounded-full bg-theme-1 mr-1">New</span> Initial Release 
                        <ul class="list-disc ml-6 mt-3">
                            <li>Side menu layout with sales & transactions dashboard. Basic components & pages.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- BEGIN: JS Assets-->
        <script src="dist/js/app.js"></script>
        <!-- END: JS Assets-->
    </body>
</html>