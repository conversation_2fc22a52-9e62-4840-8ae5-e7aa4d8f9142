.dropdown-box {
    z-index: 9999;
    transition: visibility 0s linear 0.2s, opacity 0.2s 0s;
    @apply invisible opacity-0 absolute right-0;
    &.show {
        transition: visibility 0s linear 0s, opacity 0.2s 0s;
        @apply visible opacity-100;
        > .dropdown-box__content {
            @apply mt-1;
            .tab-content .tab-content__pane {
                @apply visible;
            }
        }
    }
    .dropdown-box__content {
        transition: margin-top 0.2s;
        @apply mt-5;
        .tab-content .tab-content__pane {
            @apply invisible;
        }
    }
}