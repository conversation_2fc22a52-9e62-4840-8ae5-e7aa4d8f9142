// Fonts
@import 'fonts/roboto';

// Breakpoint
@import 'breakpoint';

// 3rd Parties
@import '~tiny-slider/src/tiny-slider';
@import '~highlight.js/scss/github';
@import '~tippy.js/dist/tippy';
@import '~tippy.js/themes/light';
@import '~tippy.js/dist/svg-arrow';
@import '~tippy.js/animations/shift-away';
@import '~tail.select/css/default/tail.select-light';
@import '~toastify-js/src/toastify';
@import '~dropzone/dist/dropzone';
@import '~zoom-vanilla.js/dist/zoom';
@import '~tabulator-tables/dist/css/tabulator';

// Animations
@import 'typing-dots';
@import 'zoom-in';
@import 'intro';

// Components
@import 'top-bar';
@import 'app';
@import 'login';
@import 'mini-chat-box';
@import 'mini-chat-list';
@import 'mini-chat';
@import 'table';
@import 'table-report';
@import 'report-timeline';
@import 'report-chart';
@import 'search';
@import 'input';
@import 'button';
@import 'notification';
@import 'image-fit';
@import 'box';
@import 'report-box';
@import 'global';
@import 'content';
@import 'top-nav';
@import 'side-nav';
@import 'breadcrumb';
@import 'tailwind';
@import 'top-bar-boxed';
@import 'mobile-menu';
@import 'pagination';
@import 'dropdown';
@import 'modal';
@import 'tab';
@import 'checkbox';
@import 'file';
@import 'inbox-filter';
@import 'inbox';
@import 'scrollbar';
@import 'chat';
@import 'boxed-tabs';
@import 'chat-dropdown';
@import 'wizard';
@import 'blog';
@import 'news';
@import 'pricing-tabs';
@import 'accordion';
@import 'error-page';
@import 'tippy';
@import 'tabulator';
@import 'tail-select';
@import 'ckeditor';
@import 'dropzone';
@import 'search-result';
@import 'mini-report-chart';
@import 'notification-content';
@import 'report-maps';
@import 'pos-dropdown';
@import 'pos';
@import 'toastify';
@import 'post';
@import 'litepicker';
@import 'tiny-slider';
@import 'pristine';
@import 'zoom-vanilla';
@import 'dark-mode-switcher';