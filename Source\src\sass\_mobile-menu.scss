.mobile-menu {
    @apply border-b border-theme-24 -mt-5 -mx-8 mb-6;
    @media (max-width: $sm) {
        @apply -mx-3;
    }
    .mobile-menu-bar {
        height: 70px;
        @apply px-8 flex items-center;
        @media (max-width: $sm) {
            @apply px-3;
        }
    }
    ul {
        &.menu__sub-open {
            @apply block;
        }
        li {
            max-width: 1280px;
            @apply w-full mx-auto;
            ul {
                @apply hidden bg-theme-28 rounded-md mx-6 my-1;
                @media (max-width: $sm) {
                    @apply mx-3;
                }
                .menu {
                    @apply px-4;
                }
                ul {
                    @apply hidden bg-theme-29 rounded-md mx-0;
                }
            }
        }
    }
    .menu__devider {
        @apply w-full h-px bg-theme-24 relative;
    }
    .menu {
        height: 50px;
        @apply flex items-center text-white px-10;
        @media (max-width: $sm) {
            @apply px-5;
        }
        .menu__title {
            @apply w-full ml-3 flex items-center;
            .menu__sub-icon {
                @apply transition ease-in duration-100 w-5 h-5 ml-auto;
            }
        }
    }
}

.dark {
    .mobile-menu {
        ul {
            li {
                ul {
                    @apply bg-dark-4;
                    ul {
                        @apply bg-dark-7;
                    }
                }
            }
        }
        .menu__devider {
            @apply bg-dark-3;
        }
        
    }
}