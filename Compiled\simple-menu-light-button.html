<!DOCTYPE html>
<!--
Template Name: Midone - HTML Admin Dashboard Template
Author: Left4code
Website: http://www.left4code.com/
Contact: muham<PERSON><PERSON><PERSON>@left4code.com
Purchase: https://themeforest.net/user/left4code/portfolio
Renew Support: https://themeforest.net/user/left4code/portfolio
License: You must have a valid license purchased only from themeforest(the above link) in order to legally use the theme for your project.
-->
<html lang="en" class="light">
    <!-- BEGIN: Head -->
    <head>
        <meta charset="utf-8">
        <link href="dist/images/logo.svg" rel="shortcut icon">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="Midone admin is super flexible, powerful, clean & modern responsive tailwind admin template with unlimited possibilities.">
        <meta name="keywords" content="admin template, Midone admin template, dashboard template, flat admin template, responsive admin template, web app">
        <meta name="author" content="LEFT4CODE">
        <title>Button - Midone - Tailwind HTML Admin Template</title>
        <!-- BEGIN: CSS Assets-->
        <link rel="stylesheet" href="dist/css/app.css" />
        <!-- END: CSS Assets-->
    </head>
    <!-- END: Head -->
    <body class="app">
        <!-- BEGIN: Mobile Menu -->
        <div class="mobile-menu md:hidden">
            <div class="mobile-menu-bar">
                <a href="" class="flex mr-auto">
                    <img alt="Midone Tailwind HTML Admin Template" class="w-6" src="dist/images/logo.svg">
                </a>
                <a href="javascript:;" id="mobile-menu-toggler"> <i data-feather="bar-chart-2" class="w-8 h-8 text-white transform -rotate-90"></i> </a>
            </div>
            <ul class="border-t border-theme-24 py-5 hidden">
                <li>
                    <a href="index.html" class="menu">
                        <div class="menu__icon"> <i data-feather="home"></i> </div>
                        <div class="menu__title"> Dashboard </div>
                    </a>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="box"></i> </div>
                        <div class="menu__title"> Menu Layout <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="index.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Side Menu </div>
                            </a>
                        </li>
                        <li>
                            <a href="simple-menu-light-dashboard.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Simple Menu </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-dashboard.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Top Menu </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="side-menu-light-inbox.html" class="menu">
                        <div class="menu__icon"> <i data-feather="inbox"></i> </div>
                        <div class="menu__title"> Inbox </div>
                    </a>
                </li>
                <li>
                    <a href="side-menu-light-file-manager.html" class="menu">
                        <div class="menu__icon"> <i data-feather="hard-drive"></i> </div>
                        <div class="menu__title"> File Manager </div>
                    </a>
                </li>
                <li>
                    <a href="side-menu-light-point-of-sale.html" class="menu">
                        <div class="menu__icon"> <i data-feather="credit-card"></i> </div>
                        <div class="menu__title"> Point of Sale </div>
                    </a>
                </li>
                <li>
                    <a href="side-menu-light-chat.html" class="menu">
                        <div class="menu__icon"> <i data-feather="message-square"></i> </div>
                        <div class="menu__title"> Chat </div>
                    </a>
                </li>
                <li>
                    <a href="side-menu-light-post.html" class="menu">
                        <div class="menu__icon"> <i data-feather="file-text"></i> </div>
                        <div class="menu__title"> Post </div>
                    </a>
                </li>
                <li class="menu__devider my-6"></li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="edit"></i> </div>
                        <div class="menu__title"> Crud <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="side-menu-light-crud-data-list.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Data List </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-crud-form.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Form </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="users"></i> </div>
                        <div class="menu__title"> Users <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="side-menu-light-users-layout-1.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Layout 1 </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-users-layout-2.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Layout 2 </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-users-layout-3.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Layout 3 </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="trello"></i> </div>
                        <div class="menu__title"> Profile <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="side-menu-light-profile-overview-1.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Overview 1 </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-profile-overview-2.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Overview 2 </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-profile-overview-3.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Overview 3 </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="layout"></i> </div>
                        <div class="menu__title"> Pages <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Wizards <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-wizard-layout-1.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-wizard-layout-2.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-wizard-layout-3.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Blog <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-blog-layout-1.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-blog-layout-2.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-blog-layout-3.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Pricing <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-pricing-layout-1.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-pricing-layout-2.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 2</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Invoice <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-invoice-layout-1.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-invoice-layout-2.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 2</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> FAQ <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-faq-layout-1.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-faq-layout-2.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-faq-layout-3.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="login-light-login.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Login </div>
                            </a>
                        </li>
                        <li>
                            <a href="login-light-register.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Register </div>
                            </a>
                        </li>
                        <li>
                            <a href="main-light-error-page.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Error Page </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-update-profile.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Update profile </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-change-password.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Change Password </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="menu__devider my-6"></li>
                <li>
                    <a href="javascript:;.html" class="menu menu--active">
                        <div class="menu__icon"> <i data-feather="inbox"></i> </div>
                        <div class="menu__title"> Components <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="menu__sub-open">
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Grid <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-regular-table.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Regular Table</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-tabulator.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Tabulator</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="side-menu-light-accordion.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Accordion </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-button.html" class="menu menu--active">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Button </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-modal.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Modal </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-alert.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Alert </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-progress-bar.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Progress Bar </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-tooltip.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Tooltip </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-dropdown.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Dropdown </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-toast.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Toast </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-typography.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Typography </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-icon.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Icon </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-loading-icon.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Loading Icon </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="sidebar"></i> </div>
                        <div class="menu__title"> Forms <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="side-menu-light-regular-form.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Regular Form </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-datepicker.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Datepicker </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-tail-select.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Tail Select </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-file-upload.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> File Upload </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-wysiwyg-editor.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Wysiwyg Editor </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-validation.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Validation </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="hard-drive"></i> </div>
                        <div class="menu__title"> Widgets <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="side-menu-light-chart.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Chart </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-slider.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Slider </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-image-zoom.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Image Zoom </div>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
        <!-- END: Mobile Menu -->
        <div class="flex">
            <!-- BEGIN: Simple Menu -->
            <nav class="side-nav side-nav--simple">
                <a href="" class="intro-x flex items-center pl-5 pt-4">
                    <img alt="Midone Tailwind HTML Admin Template" class="w-6" src="dist/images/logo.svg">
                </a>
                <div class="side-nav__devider my-6"></div>
                <ul>
                    <li>
                        <a href="simple-menu-light-dashboard.html" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="home"></i> </div>
                            <div class="side-menu__title"> Dashboard </div>
                        </a>
                    </li>
                    <li>
                        <a href="javascript:;" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="box"></i> </div>
                            <div class="side-menu__title"> Menu Layout <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                        </a>
                        <ul class="">
                            <li>
                                <a href="index.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Side Menu </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-dashboard.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Simple Menu </div>
                                </a>
                            </li>
                            <li>
                                <a href="top-menu-light-dashboard.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Top Menu </div>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <a href="simple-menu-light-inbox.html" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="inbox"></i> </div>
                            <div class="side-menu__title"> Inbox </div>
                        </a>
                    </li>
                    <li>
                        <a href="simple-menu-light-file-manager.html" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="hard-drive"></i> </div>
                            <div class="side-menu__title"> File Manager </div>
                        </a>
                    </li>
                    <li>
                        <a href="simple-menu-light-point-of-sale.html" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="credit-card"></i> </div>
                            <div class="side-menu__title"> Point of Sale </div>
                        </a>
                    </li>
                    <li>
                        <a href="simple-menu-light-chat.html" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="message-square"></i> </div>
                            <div class="side-menu__title"> Chat </div>
                        </a>
                    </li>
                    <li>
                        <a href="simple-menu-light-post.html" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="file-text"></i> </div>
                            <div class="side-menu__title"> Post </div>
                        </a>
                    </li>
                    <li class="side-nav__devider my-6"></li>
                    <li>
                        <a href="javascript:;" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="edit"></i> </div>
                            <div class="side-menu__title"> Crud <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                        </a>
                        <ul class="">
                            <li>
                                <a href="side-menu-light-crud-data-list.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Data List </div>
                                </a>
                            </li>
                            <li>
                                <a href="side-menu-light-crud-form.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Form </div>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <a href="javascript:;" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="users"></i> </div>
                            <div class="side-menu__title"> Users <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                        </a>
                        <ul class="">
                            <li>
                                <a href="simple-menu-light-users-layout-1.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Layout 1 </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-users-layout-2.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Layout 2 </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-users-layout-3.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Layout 3 </div>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <a href="javascript:;" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="trello"></i> </div>
                            <div class="side-menu__title"> Profile <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                        </a>
                        <ul class="">
                            <li>
                                <a href="simple-menu-light-profile-overview-1.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Overview 1 </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-profile-overview-2.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Overview 2 </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-profile-overview-3.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Overview 3 </div>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <a href="javascript:;" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="layout"></i> </div>
                            <div class="side-menu__title"> Pages <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                        </a>
                        <ul class="">
                            <li>
                                <a href="javascript:;" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Wizards <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                                </a>
                                <ul class="">
                                    <li>
                                        <a href="simple-menu-light-wizard-layout-1.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 1</div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="simple-menu-light-wizard-layout-2.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 2</div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="simple-menu-light-wizard-layout-3.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 3</div>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <a href="javascript:;" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Blog <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                                </a>
                                <ul class="">
                                    <li>
                                        <a href="simple-menu-light-blog-layout-1.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 1</div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="simple-menu-light-blog-layout-2.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 2</div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="simple-menu-light-blog-layout-3.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 3</div>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <a href="javascript:;" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Pricing <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                                </a>
                                <ul class="">
                                    <li>
                                        <a href="simple-menu-light-pricing-layout-1.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 1</div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="simple-menu-light-pricing-layout-2.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 2</div>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <a href="javascript:;" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Invoice <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                                </a>
                                <ul class="">
                                    <li>
                                        <a href="simple-menu-light-invoice-layout-1.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 1</div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="simple-menu-light-invoice-layout-2.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 2</div>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <a href="javascript:;" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> FAQ <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                                </a>
                                <ul class="">
                                    <li>
                                        <a href="simple-menu-light-faq-layout-1.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 1</div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="simple-menu-light-faq-layout-2.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 2</div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="simple-menu-light-faq-layout-3.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Layout 3</div>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <a href="login-light-login.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Login </div>
                                </a>
                            </li>
                            <li>
                                <a href="login-light-register.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Register </div>
                                </a>
                            </li>
                            <li>
                                <a href="main-light-error-page.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Error Page </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-update-profile.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Update profile </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-change-password.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Change Password </div>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="side-nav__devider my-6"></li>
                    <li>
                        <a href="javascript:;.html" class="side-menu side-menu--active">
                            <div class="side-menu__icon"> <i data-feather="inbox"></i> </div>
                            <div class="side-menu__title"> Components <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                        </a>
                        <ul class="side-menu__sub-open">
                            <li>
                                <a href="javascript:;" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Grid <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                                </a>
                                <ul class="">
                                    <li>
                                        <a href="simple-menu-light-regular-table.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Regular Table</div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="simple-menu-light-tabulator.html" class="side-menu">
                                            <div class="side-menu__icon"> <i data-feather="zap"></i> </div>
                                            <div class="side-menu__title">Tabulator</div>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li>
                                <a href="simple-menu-light-accordion.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Accordion </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-button.html" class="side-menu side-menu--active">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Button </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-modal.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Modal </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-alert.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Alert </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-progress-bar.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Progress Bar </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-tooltip.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Tooltip </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-dropdown.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Dropdown </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-toast.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Toast </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-typography.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Typography </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-icon.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Icon </div>
                                </a>
                            </li>
                            <li>
                                <a href="side-menu-light-loading-icon.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Loading Icon </div>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <a href="javascript:;" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="sidebar"></i> </div>
                            <div class="side-menu__title"> Forms <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                        </a>
                        <ul class="">
                            <li>
                                <a href="simple-menu-light-regular-form.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Regular Form </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-datepicker.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Datepicker </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-tail-select.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Tail Select </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-file-upload.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> File Upload </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-wysiwyg-editor.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Wysiwyg Editor </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-validation.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Validation </div>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <a href="javascript:;" class="side-menu">
                            <div class="side-menu__icon"> <i data-feather="hard-drive"></i> </div>
                            <div class="side-menu__title"> Widgets <i data-feather="chevron-down" class="side-menu__sub-icon"></i> </div>
                        </a>
                        <ul class="">
                            <li>
                                <a href="simple-menu-light-chart.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Chart </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-slider.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Slider </div>
                                </a>
                            </li>
                            <li>
                                <a href="simple-menu-light-image-zoom.html" class="side-menu">
                                    <div class="side-menu__icon"> <i data-feather="activity"></i> </div>
                                    <div class="side-menu__title"> Image Zoom </div>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
            <!-- END: Simple Menu -->
            <!-- BEGIN: Content -->
            <div class="content">
                <!-- BEGIN: Top Bar -->
                <div class="top-bar">
                    <!-- BEGIN: Breadcrumb -->
                    <div class="-intro-x breadcrumb mr-auto hidden sm:flex"> <a href="" class="">Application</a> <i data-feather="chevron-right" class="breadcrumb__icon"></i> <a href="" class="breadcrumb--active">Dashboard</a> </div>
                    <!-- END: Breadcrumb -->
                    <!-- BEGIN: Search -->
                    <div class="intro-x relative mr-3 sm:mr-6">
                        <div class="search hidden sm:block">
                            <input type="text" class="search__input input placeholder-theme-13" placeholder="Search...">
                            <i data-feather="search" class="search__icon dark:text-gray-300"></i> 
                        </div>
                        <a class="notification sm:hidden" href=""> <i data-feather="search" class="notification__icon dark:text-gray-300"></i> </a>
                        <div class="search-result">
                            <div class="search-result__content">
                                <div class="search-result__content__title">Pages</div>
                                <div class="mb-5">
                                    <a href="" class="flex items-center">
                                        <div class="w-8 h-8 bg-theme-18 text-theme-9 flex items-center justify-center rounded-full"> <i class="w-4 h-4" data-feather="inbox"></i> </div>
                                        <div class="ml-3">Mail Settings</div>
                                    </a>
                                    <a href="" class="flex items-center mt-2">
                                        <div class="w-8 h-8 bg-theme-17 text-theme-11 flex items-center justify-center rounded-full"> <i class="w-4 h-4" data-feather="users"></i> </div>
                                        <div class="ml-3">Users & Permissions</div>
                                    </a>
                                    <a href="" class="flex items-center mt-2">
                                        <div class="w-8 h-8 bg-theme-14 text-theme-10 flex items-center justify-center rounded-full"> <i class="w-4 h-4" data-feather="credit-card"></i> </div>
                                        <div class="ml-3">Transactions Report</div>
                                    </a>
                                </div>
                                <div class="search-result__content__title">Users</div>
                                <div class="mb-5">
                                    <a href="" class="flex items-center mt-2">
                                        <div class="w-8 h-8 image-fit">
                                            <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-13.jpg">
                                        </div>
                                        <div class="ml-3">Morgan Freeman</div>
                                        <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right"><EMAIL></div>
                                    </a>
                                    <a href="" class="flex items-center mt-2">
                                        <div class="w-8 h-8 image-fit">
                                            <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-8.jpg">
                                        </div>
                                        <div class="ml-3">Hugh Jackman</div>
                                        <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right"><EMAIL></div>
                                    </a>
                                    <a href="" class="flex items-center mt-2">
                                        <div class="w-8 h-8 image-fit">
                                            <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-7.jpg">
                                        </div>
                                        <div class="ml-3">Brad Pitt</div>
                                        <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right"><EMAIL></div>
                                    </a>
                                    <a href="" class="flex items-center mt-2">
                                        <div class="w-8 h-8 image-fit">
                                            <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-10.jpg">
                                        </div>
                                        <div class="ml-3">Al Pacino</div>
                                        <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right"><EMAIL></div>
                                    </a>
                                </div>
                                <div class="search-result__content__title">Products</div>
                                <a href="" class="flex items-center mt-2">
                                    <div class="w-8 h-8 image-fit">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/preview-14.jpg">
                                    </div>
                                    <div class="ml-3">Dell XPS 13</div>
                                    <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right">PC &amp; Laptop</div>
                                </a>
                                <a href="" class="flex items-center mt-2">
                                    <div class="w-8 h-8 image-fit">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/preview-8.jpg">
                                    </div>
                                    <div class="ml-3">Nikon Z6</div>
                                    <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right">Photography</div>
                                </a>
                                <a href="" class="flex items-center mt-2">
                                    <div class="w-8 h-8 image-fit">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/preview-12.jpg">
                                    </div>
                                    <div class="ml-3">Dell XPS 13</div>
                                    <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right">PC &amp; Laptop</div>
                                </a>
                                <a href="" class="flex items-center mt-2">
                                    <div class="w-8 h-8 image-fit">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/preview-9.jpg">
                                    </div>
                                    <div class="ml-3">Sony Master Series A9G</div>
                                    <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right">Electronic</div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- END: Search -->
                    <!-- BEGIN: Notifications -->
                    <div class="intro-x dropdown mr-auto sm:mr-6">
                        <div class="dropdown-toggle notification notification--bullet cursor-pointer"> <i data-feather="bell" class="notification__icon dark:text-gray-300"></i> </div>
                        <div class="notification-content pt-2 dropdown-box">
                            <div class="notification-content__box dropdown-box__content box dark:bg-dark-6">
                                <div class="notification-content__title">Notifications</div>
                                <div class="cursor-pointer relative flex items-center ">
                                    <div class="w-12 h-12 flex-none image-fit mr-1">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-13.jpg">
                                        <div class="w-3 h-3 bg-theme-9 absolute right-0 bottom-0 rounded-full border-2 border-white"></div>
                                    </div>
                                    <div class="ml-2 overflow-hidden">
                                        <div class="flex items-center">
                                            <a href="javascript:;" class="font-medium truncate mr-5">Morgan Freeman</a> 
                                            <div class="text-xs text-gray-500 ml-auto whitespace-no-wrap">01:10 PM</div>
                                        </div>
                                        <div class="w-full truncate text-gray-600">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&#039;s standard dummy text ever since the 1500</div>
                                    </div>
                                </div>
                                <div class="cursor-pointer relative flex items-center mt-5">
                                    <div class="w-12 h-12 flex-none image-fit mr-1">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-8.jpg">
                                        <div class="w-3 h-3 bg-theme-9 absolute right-0 bottom-0 rounded-full border-2 border-white"></div>
                                    </div>
                                    <div class="ml-2 overflow-hidden">
                                        <div class="flex items-center">
                                            <a href="javascript:;" class="font-medium truncate mr-5">Hugh Jackman</a> 
                                            <div class="text-xs text-gray-500 ml-auto whitespace-no-wrap">06:05 AM</div>
                                        </div>
                                        <div class="w-full truncate text-gray-600">There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomi</div>
                                    </div>
                                </div>
                                <div class="cursor-pointer relative flex items-center mt-5">
                                    <div class="w-12 h-12 flex-none image-fit mr-1">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-7.jpg">
                                        <div class="w-3 h-3 bg-theme-9 absolute right-0 bottom-0 rounded-full border-2 border-white"></div>
                                    </div>
                                    <div class="ml-2 overflow-hidden">
                                        <div class="flex items-center">
                                            <a href="javascript:;" class="font-medium truncate mr-5">Brad Pitt</a> 
                                            <div class="text-xs text-gray-500 ml-auto whitespace-no-wrap">05:09 AM</div>
                                        </div>
                                        <div class="w-full truncate text-gray-600">Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 20</div>
                                    </div>
                                </div>
                                <div class="cursor-pointer relative flex items-center mt-5">
                                    <div class="w-12 h-12 flex-none image-fit mr-1">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-10.jpg">
                                        <div class="w-3 h-3 bg-theme-9 absolute right-0 bottom-0 rounded-full border-2 border-white"></div>
                                    </div>
                                    <div class="ml-2 overflow-hidden">
                                        <div class="flex items-center">
                                            <a href="javascript:;" class="font-medium truncate mr-5">Al Pacino</a> 
                                            <div class="text-xs text-gray-500 ml-auto whitespace-no-wrap">01:10 PM</div>
                                        </div>
                                        <div class="w-full truncate text-gray-600">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&#039;s standard dummy text ever since the 1500</div>
                                    </div>
                                </div>
                                <div class="cursor-pointer relative flex items-center mt-5">
                                    <div class="w-12 h-12 flex-none image-fit mr-1">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-11.jpg">
                                        <div class="w-3 h-3 bg-theme-9 absolute right-0 bottom-0 rounded-full border-2 border-white"></div>
                                    </div>
                                    <div class="ml-2 overflow-hidden">
                                        <div class="flex items-center">
                                            <a href="javascript:;" class="font-medium truncate mr-5">Tom Cruise</a> 
                                            <div class="text-xs text-gray-500 ml-auto whitespace-no-wrap">03:20 PM</div>
                                        </div>
                                        <div class="w-full truncate text-gray-600">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: Notifications -->
                    <!-- BEGIN: Account Menu -->
                    <div class="intro-x dropdown w-8 h-8">
                        <div class="dropdown-toggle w-8 h-8 rounded-full overflow-hidden shadow-lg image-fit zoom-in">
                            <img alt="Midone Tailwind HTML Admin Template" src="dist/images/profile-3.jpg">
                        </div>
                        <div class="dropdown-box w-56">
                            <div class="dropdown-box__content box bg-theme-38 dark:bg-dark-6 text-white">
                                <div class="p-4 border-b border-theme-40 dark:border-dark-3">
                                    <div class="font-medium">Morgan Freeman</div>
                                    <div class="text-xs text-theme-41 dark:text-gray-600">Frontend Engineer</div>
                                </div>
                                <div class="p-2">
                                    <a href="" class="flex items-center block p-2 transition duration-300 ease-in-out hover:bg-theme-1 dark:hover:bg-dark-3 rounded-md"> <i data-feather="user" class="w-4 h-4 mr-2"></i> Profile </a>
                                    <a href="" class="flex items-center block p-2 transition duration-300 ease-in-out hover:bg-theme-1 dark:hover:bg-dark-3 rounded-md"> <i data-feather="edit" class="w-4 h-4 mr-2"></i> Add Account </a>
                                    <a href="" class="flex items-center block p-2 transition duration-300 ease-in-out hover:bg-theme-1 dark:hover:bg-dark-3 rounded-md"> <i data-feather="lock" class="w-4 h-4 mr-2"></i> Reset Password </a>
                                    <a href="" class="flex items-center block p-2 transition duration-300 ease-in-out hover:bg-theme-1 dark:hover:bg-dark-3 rounded-md"> <i data-feather="help-circle" class="w-4 h-4 mr-2"></i> Help </a>
                                </div>
                                <div class="p-2 border-t border-theme-40 dark:border-dark-3">
                                    <a href="" class="flex items-center block p-2 transition duration-300 ease-in-out hover:bg-theme-1 dark:hover:bg-dark-3 rounded-md"> <i data-feather="toggle-right" class="w-4 h-4 mr-2"></i> Logout </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: Account Menu -->
                </div>
                <!-- END: Top Bar -->
                <div class="intro-y flex items-center mt-8">
                    <h2 class="text-lg font-medium mr-auto">
                        Buttons
                    </h2>
                </div>
                <div class="intro-y grid grid-cols-12 gap-6 mt-5">
                    <div class="col-span-12 lg:col-span-6">
                        <!-- BEGIN: Basic Button -->
                        <div class="intro-y box">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Basic Buttons
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#basic-button" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="basic-button">
                                <div class="preview">
                                    <button class="button w-24 mr-1 mb-2 bg-theme-1 text-white">Primary</button>
                                    <button class="button w-24 mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300">Secondary</button>
                                    <button class="button w-24 mr-1 mb-2 bg-theme-9 text-white">Success</button>
                                    <button class="button w-24 mr-1 mb-2 bg-theme-12 text-white">Warning</button>
                                    <button class="button w-24 mr-1 mb-2 bg-theme-6 text-white">Danger</button>
                                    <button class="button w-24 mr-1 mb-2 bg-gray-200 text-gray-600">Dark</button>
                                </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-basic-button" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-basic-button"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagbutton class=&quot;button w-24 mr-1 mb-2 bg-theme-1 text-white&quot;HTMLCloseTagPrimaryHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300&quot;HTMLCloseTagSecondaryHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 mr-1 mb-2 bg-theme-9 text-white&quot;HTMLCloseTagSuccessHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 mr-1 mb-2 bg-theme-12 text-white&quot;HTMLCloseTagWarningHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 mr-1 mb-2 bg-theme-6 text-white&quot;HTMLCloseTagDangerHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 mr-1 mb-2 bg-gray-200 text-gray-600&quot;HTMLCloseTagDarkHTMLOpenTag/buttonHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Basic Button -->
                        <!-- BEGIN: Button Size -->
                        <div class="intro-y box mt-5">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Button Sizes
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#button-size" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="button-size">
                                <div class="preview">
                                    <div>
                                        <button class="button button--sm w-24 mr-1 mb-2 bg-theme-1 text-white">Small</button>
                                        <button class="button w-24 mr-1 mb-2 bg-theme-1 text-white">Medium</button>
                                        <button class="button button--lg w-24 mr-1 mb-2 bg-theme-1 text-white">Large</button>
                                    </div>
                                    <div class="mt-5">
                                        <button class="button button--sm w-24 mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300">Small</button>
                                        <button class="button w-24 mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300">Medium</button>
                                        <button class="button button--lg w-24 mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300">Large</button>
                                    </div>
                                </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-button-size" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-button-size"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagdivHTMLCloseTag HTMLOpenTagbutton class=&quot;button button--sm w-24 mr-1 mb-2 bg-theme-1 text-white&quot;HTMLCloseTagSmallHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 mr-1 mb-2 bg-theme-1 text-white&quot;HTMLCloseTagMediumHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button button--lg w-24 mr-1 mb-2 bg-theme-1 text-white&quot;HTMLCloseTagLargeHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-5&quot;HTMLCloseTag HTMLOpenTagbutton class=&quot;button button--sm w-24 mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300&quot;HTMLCloseTagSmallHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300&quot;HTMLCloseTagMediumHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button button--lg w-24 mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300&quot;HTMLCloseTagLargeHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTag/divHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Button Size -->
                        <!-- BEGIN: Button Link -->
                        <div class="intro-y box mt-5">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Working with Links
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#link-button" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="link-button">
                                <div class="preview"> <a href="" class="button w-24 inline-block mr-1 mb-2 bg-theme-1 text-white">Link</a> <a href="" class="button w-24 inline-block mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300">Button</a> <a href="" class="button w-24 inline-block mr-1 mb-2 bg-theme-9 text-white">Input</a> <a href="" class="button w-24 inline-block mr-1 mb-2 bg-theme-12 text-white">Submit</a> <a href="" class="button w-24 inline-block mr-1 mb-2 bg-theme-6 text-white">Reset</a> <a href="" class="button w-24 inline-block mr-1 mb-2 bg-gray-200 text-gray-600">Metal</a> </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-link-button" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-link-button"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTaga href=&quot;&quot; class=&quot;button w-24 inline-block mr-1 mb-2 bg-theme-1 text-white&quot;HTMLCloseTagLinkHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;button w-24 inline-block mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300&quot;HTMLCloseTagButtonHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;button w-24 inline-block mr-1 mb-2 bg-theme-9 text-white&quot;HTMLCloseTagInputHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;button w-24 inline-block mr-1 mb-2 bg-theme-12 text-white&quot;HTMLCloseTagSubmitHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;button w-24 inline-block mr-1 mb-2 bg-theme-6 text-white&quot;HTMLCloseTagResetHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;button w-24 inline-block mr-1 mb-2 bg-gray-200 text-gray-600&quot;HTMLCloseTagMetalHTMLOpenTag/aHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Button Link -->
                        <!-- BEGIN: Elevated Button -->
                        <div class="intro-y box mt-5">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Elevated Buttons
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#elevated-button" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="elevated-button">
                                <div class="preview">
                                    <div>
                                        <button class="button w-24 shadow-md mr-1 mb-2 bg-theme-1 text-white">Primary</button>
                                        <button class="button w-24 shadow-md mr-1 mb-2 text-gray-700 dark:bg-dark-5 dark:text-gray-300">Secondary</button>
                                        <button class="button w-24 shadow-md mr-1 mb-2 bg-theme-9 text-white">Success</button>
                                        <button class="button w-24 shadow-md mr-1 mb-2 bg-theme-12 text-white">Warning</button>
                                        <button class="button w-24 shadow-md mr-1 mb-2 bg-theme-6 text-white">Danger</button>
                                        <button class="button w-24 shadow-md mr-1 mb-2 bg-gray-200 text-gray-600">Dark</button>
                                    </div>
                                    <div class="mt-5">
                                        <button class="button w-24 rounded-full shadow-md mr-1 mb-2 bg-theme-1 text-white">Primary</button>
                                        <button class="button w-24 rounded-full shadow-md mr-1 mb-2 text-gray-700 dark:bg-dark-5 dark:text-gray-300">Secondary</button>
                                        <button class="button w-24 rounded-full shadow-md mr-1 mb-2 bg-theme-9 text-white">Success</button>
                                        <button class="button w-24 rounded-full shadow-md mr-1 mb-2 bg-theme-12 text-white">Warning</button>
                                        <button class="button w-24 rounded-full shadow-md mr-1 mb-2 bg-theme-6 text-white">Danger</button>
                                        <button class="button w-24 rounded-full shadow-md mr-1 mb-2 bg-gray-200 text-gray-600">Dark</button>
                                    </div>
                                </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-elevated-button" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-elevated-button"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagdivHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 shadow-md mr-1 mb-2 bg-theme-1 text-white&quot;HTMLCloseTagPrimaryHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 shadow-md mr-1 mb-2 text-gray-700 dark:bg-dark-5 dark:text-gray-300&quot;HTMLCloseTagSecondaryHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 shadow-md mr-1 mb-2 bg-theme-9 text-white&quot;HTMLCloseTagSuccessHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 shadow-md mr-1 mb-2 bg-theme-12 text-white&quot;HTMLCloseTagWarningHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 shadow-md mr-1 mb-2 bg-theme-6 text-white&quot;HTMLCloseTagDangerHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 shadow-md mr-1 mb-2 bg-gray-200 text-gray-600&quot;HTMLCloseTagDarkHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-5&quot;HTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full shadow-md mr-1 mb-2 bg-theme-1 text-white&quot;HTMLCloseTagPrimaryHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full shadow-md mr-1 mb-2 text-gray-700 dark:bg-dark-5 dark:text-gray-300&quot;HTMLCloseTagSecondaryHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full shadow-md mr-1 mb-2 bg-theme-9 text-white&quot;HTMLCloseTagSuccessHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full shadow-md mr-1 mb-2 bg-theme-12 text-white&quot;HTMLCloseTagWarningHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full shadow-md mr-1 mb-2 bg-theme-6 text-white&quot;HTMLCloseTagDangerHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full shadow-md mr-1 mb-2 bg-gray-200 text-gray-600&quot;HTMLCloseTagDarkHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTag/divHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Elevated Button -->
                        <!-- BEGIN: Social Media Button -->
                        <div class="intro-y box mt-5">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Social Media Buttons
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#social-media-button" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="social-media-button">
                                <div class="preview">
                                    <div class="flex flex-wrap">
                                        <button class="button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-32 text-white"> <i data-feather="facebook" class="w-4 h-4 mr-2"></i> Facebook </button>
                                        <button class="button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-33 text-white"> <i data-feather="twitter" class="w-4 h-4 mr-2"></i> Twitter </button>
                                        <button class="button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-34 text-white"> <i data-feather="instagram" class="w-4 h-4 mr-2"></i> Instagram </button>
                                        <button class="button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-35 text-white"> <i data-feather="linkedin" class="w-4 h-4 mr-2"></i> Linkedin </button>
                                    </div>
                                </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-social-media-button" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-social-media-button"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagbutton class=&quot;button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-32 text-white&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;facebook&quot; class=&quot;w-4 h-4 mr-2&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag Facebook HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-33 text-white&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;twitter&quot; class=&quot;w-4 h-4 mr-2&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag Twitter HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-34 text-white&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;instagram&quot; class=&quot;w-4 h-4 mr-2&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag Instagram HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-35 text-white&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;linkedin&quot; class=&quot;w-4 h-4 mr-2&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag Linkedin HTMLOpenTag/buttonHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Social Media Button -->
                    </div>
                    <div class="col-span-12 lg:col-span-6">
                        <!-- BEGIN: Outline Button -->
                        <div class="intro-y box">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Outline Buttons
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#outline-button" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="outline-button">
                                <div class="preview">
                                    <button class="button w-24 inline-block mr-1 mb-2 border border-theme-1 text-theme-1 dark:border-theme-10 dark:text-theme-10">Link</button>
                                    <button class="button w-24 inline-block mr-1 mb-2 border text-gray-700 dark:border-dark-5 dark:text-gray-300">Button</button>
                                    <button class="button w-24 inline-block mr-1 mb-2 border border-theme-9 text-theme-9 dark:border-theme-9">Input</button>
                                    <button class="button w-24 inline-block mr-1 mb-2 border border-theme-12 text-theme-12 dark:border-theme-12">Submit</button>
                                    <button class="button w-24 inline-block mr-1 mb-2 border border-theme-6 text-theme-6 dark:border-theme-6">Reset</button>
                                </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-outline-button" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-outline-button"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagbutton class=&quot;button w-24 inline-block mr-1 mb-2 border border-theme-1 text-theme-1 dark:border-theme-10 dark:text-theme-10&quot;HTMLCloseTagLinkHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 inline-block mr-1 mb-2 border text-gray-700 dark:border-dark-5 dark:text-gray-300&quot;HTMLCloseTagButtonHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 inline-block mr-1 mb-2 border border-theme-9 text-theme-9 dark:border-theme-9&quot;HTMLCloseTagInputHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 inline-block mr-1 mb-2 border border-theme-12 text-theme-12 dark:border-theme-12&quot;HTMLCloseTagSubmitHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 inline-block mr-1 mb-2 border border-theme-6 text-theme-6 dark:border-theme-6&quot;HTMLCloseTagResetHTMLOpenTag/buttonHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Outline Button -->
                        <!-- BEGIN: Loading Button -->
                        <div class="intro-y box mt-5">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Loading State Buttons
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#loading-button" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="loading-button">
                                <div class="preview">
                                    <button class="button w-24 inline-block mr-1 mb-2 bg-theme-1 text-white inline-flex items-center"> Saving <i data-loading-icon="oval" data-color="white" class="w-4 h-4 ml-auto"></i> </button>
                                    <button class="button w-24 inline-block mr-1 mb-2 bg-theme-9 text-white inline-flex items-center"> Adding <i data-loading-icon="spinning-circles" data-color="white" class="w-4 h-4 ml-auto"></i> </button>
                                    <button class="button w-24 inline-block mr-1 mb-2 bg-theme-12 text-white inline-flex items-center"> Loading <i data-loading-icon="three-dots" data-color="white" class="w-4 h-4 ml-auto"></i> </button>
                                    <button class="button w-24 inline-block mr-1 mb-2 bg-theme-6 text-white inline-flex items-center"> Deleting <i data-loading-icon="puff" data-color="white" class="w-4 h-4 ml-auto"></i> </button>
                                </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-loading-button" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-loading-button"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagbutton class=&quot;button w-24 inline-block mr-1 mb-2 bg-theme-1 text-white inline-flex items-center&quot;HTMLCloseTag Saving HTMLOpenTagi data-loading-icon=&quot;oval&quot; data-color=&quot;white&quot; class=&quot;w-4 h-4 ml-auto&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 inline-block mr-1 mb-2 bg-theme-9 text-white inline-flex items-center&quot;HTMLCloseTag Adding HTMLOpenTagi data-loading-icon=&quot;spinning-circles&quot; data-color=&quot;white&quot; class=&quot;w-4 h-4 ml-auto&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 inline-block mr-1 mb-2 bg-theme-12 text-white inline-flex items-center&quot;HTMLCloseTag Loading HTMLOpenTagi data-loading-icon=&quot;three-dots&quot; data-color=&quot;white&quot; class=&quot;w-4 h-4 ml-auto&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 inline-block mr-1 mb-2 bg-theme-6 text-white inline-flex items-center&quot;HTMLCloseTag Deleting HTMLOpenTagi data-loading-icon=&quot;puff&quot; data-color=&quot;white&quot; class=&quot;w-4 h-4 ml-auto&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag HTMLOpenTag/buttonHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Loading Button -->
                        <!-- BEGIN: Rounded Button -->
                        <div class="intro-y box mt-5">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Rounded Buttons
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#rounded-button" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="rounded-button">
                                <div class="preview">
                                    <button class="button w-24 rounded-full mr-1 mb-2 bg-theme-1 text-white">Primary</button>
                                    <button class="button w-24 rounded-full mr-1 mb-2 border text-gray-700 dark:border-dark-5 dark:text-gray-300">Secondary</button>
                                    <button class="button w-24 rounded-full mr-1 mb-2 bg-theme-9 text-white">Success</button>
                                    <button class="button w-24 rounded-full mr-1 mb-2 bg-theme-12 text-white">Warning</button>
                                    <button class="button w-24 rounded-full mr-1 mb-2 bg-theme-6 text-white">Danger</button>
                                    <button class="button w-24 rounded-full mr-1 mb-2 bg-gray-200 text-gray-600">Dark</button>
                                </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-rounded-button" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-rounded-button"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 bg-theme-1 text-white&quot;HTMLCloseTagPrimaryHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 border text-gray-700 dark:border-dark-5 dark:text-gray-300&quot;HTMLCloseTagSecondaryHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 bg-theme-9 text-white&quot;HTMLCloseTagSuccessHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 bg-theme-12 text-white&quot;HTMLCloseTagWarningHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 bg-theme-6 text-white&quot;HTMLCloseTagDangerHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 bg-gray-200 text-gray-600&quot;HTMLCloseTagDarkHTMLOpenTag/buttonHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Rounded Button -->
                        <!-- BEGIN: Soft Color Button -->
                        <div class="intro-y box mt-5">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Soft Color Buttons
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#softcolor-button" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="softcolor-button">
                                <div class="preview">
                                    <button class="button w-24 rounded-full mr-1 mb-2 bg-theme-14 text-theme-10">Primary</button>
                                    <button class="button w-24 rounded-full mr-1 mb-2 border text-gray-700 dark:border-dark-5 dark:text-gray-300">Secondary</button>
                                    <button class="button w-24 rounded-full mr-1 mb-2 bg-theme-18 text-theme-9">Success</button>
                                    <button class="button w-24 rounded-full mr-1 mb-2 bg-theme-17 text-theme-11">Warning</button>
                                    <button class="button w-24 rounded-full mr-1 mb-2 bg-theme-31 text-theme-6">Danger</button>
                                    <button class="button w-24 rounded-full mr-1 mb-2 bg-gray-200 text-gray-600">Dark</button>
                                </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-softcolor-button" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-softcolor-button"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 bg-theme-14 text-theme-10&quot;HTMLCloseTagPrimaryHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 border text-gray-700 dark:border-dark-5 dark:text-gray-300&quot;HTMLCloseTagSecondaryHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 bg-theme-18 text-theme-9&quot;HTMLCloseTagSuccessHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 bg-theme-17 text-theme-11&quot;HTMLCloseTagWarningHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 bg-theme-31 text-theme-6&quot;HTMLCloseTagDangerHTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-24 rounded-full mr-1 mb-2 bg-gray-200 text-gray-600&quot;HTMLCloseTagDarkHTMLOpenTag/buttonHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Soft Color Button -->
                        <!-- BEGIN: Icon Button -->
                        <div class="intro-y box mt-5">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Icon Buttons
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#icon-button" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="icon-button">
                                <div class="preview">
                                    <div class="flex flex-wrap">
                                        <button class="button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-1 text-white"> <i data-feather="activity" class="w-4 h-4 mr-2"></i> Activity </button>
                                        <button class="button w-32 mr-2 mb-2 flex items-center justify-center border text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="hard-drive" class="w-4 h-4 mr-2"></i> Hard Drive </button>
                                        <button class="button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-9 text-white"> <i data-feather="calendar" class="w-4 h-4 mr-2"></i> Calendar </button>
                                        <button class="button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-12 text-white"> <i data-feather="share-2" class="w-4 h-4 mr-2"></i> Share </button>
                                        <button class="button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-6 text-white"> <i data-feather="trash" class="w-4 h-4 mr-2"></i> Trash </button>
                                        <button class="button w-32 mr-2 mb-2 flex items-center justify-center bg-gray-200 text-gray-600"> <i data-feather="image" class="w-4 h-4 mr-2"></i> Image </button>
                                    </div>
                                </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-icon-button" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-icon-button"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagbutton class=&quot;button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-1 text-white&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;activity&quot; class=&quot;w-4 h-4 mr-2&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag Activity HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-32 mr-2 mb-2 flex items-center justify-center border text-gray-700 dark:border-dark-5 dark:text-gray-300&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;hard-drive&quot; class=&quot;w-4 h-4 mr-2&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag Hard Drive HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-9 text-white&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;calendar&quot; class=&quot;w-4 h-4 mr-2&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag Calendar HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-12 text-white&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;share-2&quot; class=&quot;w-4 h-4 mr-2&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag Share HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-32 mr-2 mb-2 flex items-center justify-center bg-theme-6 text-white&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;trash&quot; class=&quot;w-4 h-4 mr-2&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag Trash HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button w-32 mr-2 mb-2 flex items-center justify-center bg-gray-200 text-gray-600&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;image&quot; class=&quot;w-4 h-4 mr-2&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag Image HTMLOpenTag/buttonHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Icon Button -->
                        <!-- BEGIN: Icon Only Button -->
                        <div class="intro-y box mt-5">
                            <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                                <h2 class="font-medium text-base mr-auto">
                                    Icon Only Buttons
                                </h2>
                                <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                    <div class="mr-3">Show example code</div>
                                    <input data-target="#icon-only-button" class="show-code input input--switch border" type="checkbox">
                                </div>
                            </div>
                            <div class="p-5" id="icon-only-button">
                                <div class="preview">
                                    <button class="button px-2 mr-1 mb-2 bg-theme-1 text-white">
                                        <span class="w-5 h-5 flex items-center justify-center"> <i data-feather="activity" class="w-4 h-4"></i> </span>
                                    </button>
                                    <button class="button px-2 mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300">
                                        <span class="w-5 h-5 flex items-center justify-center"> <i data-feather="hard-drive" class="w-4 h-4"></i> </span>
                                    </button>
                                    <button class="button px-2 mr-1 mb-2 bg-theme-9 text-white">
                                        <span class="w-5 h-5 flex items-center justify-center"> <i data-feather="calendar" class="w-4 h-4"></i> </span>
                                    </button>
                                    <button class="button px-2 mr-1 mb-2 bg-theme-12 text-white">
                                        <span class="w-5 h-5 flex items-center justify-center"> <i data-feather="share-2" class="w-4 h-4"></i> </span>
                                    </button>
                                    <button class="button px-2 mr-1 mb-2 bg-theme-6 text-white">
                                        <span class="w-5 h-5 flex items-center justify-center"> <i data-feather="trash" class="w-4 h-4"></i> </span>
                                    </button>
                                    <button class="button px-2 mr-1 mb-2 bg-gray-200 text-gray-600">
                                        <span class="w-5 h-5 flex items-center justify-center"> <i data-feather="image" class="w-4 h-4"></i> </span>
                                    </button>
                                </div>
                                <div class="source-code hidden">
                                    <button data-target="#copy-icon-only-button" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                    <div class="overflow-y-auto h-64 mt-3">
                                        <pre class="source-preview" id="copy-icon-only-button"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagbutton class=&quot;button px-2 mr-1 mb-2 bg-theme-1 text-white&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;w-5 h-5 flex items-center justify-center&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;activity&quot; class=&quot;w-4 h-4&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button px-2 mr-1 mb-2 border text-gray-700 dark:bg-dark-5 dark:text-gray-300&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;w-5 h-5 flex items-center justify-center&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;hard-drive&quot; class=&quot;w-4 h-4&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button px-2 mr-1 mb-2 bg-theme-9 text-white&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;w-5 h-5 flex items-center justify-center&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;calendar&quot; class=&quot;w-4 h-4&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button px-2 mr-1 mb-2 bg-theme-12 text-white&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;w-5 h-5 flex items-center justify-center&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;share-2&quot; class=&quot;w-4 h-4&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button px-2 mr-1 mb-2 bg-theme-6 text-white&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;w-5 h-5 flex items-center justify-center&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;trash&quot; class=&quot;w-4 h-4&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/buttonHTMLCloseTag HTMLOpenTagbutton class=&quot;button px-2 mr-1 mb-2 bg-gray-200 text-gray-600&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;w-5 h-5 flex items-center justify-center&quot;HTMLCloseTag HTMLOpenTagi data-feather=&quot;image&quot; class=&quot;w-4 h-4&quot;HTMLCloseTagHTMLOpenTag/iHTMLCloseTag HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/buttonHTMLCloseTag </code> </pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Icon Only Button -->
                    </div>
                </div>
            </div>
            <!-- END: Content -->
        </div>
        <!-- BEGIN: Dark Mode Switcher-->
        <div data-url="simple-menu-dark-button.html" class="dark-mode-switcher cursor-pointer shadow-md fixed bottom-0 right-0 box dark:bg-dark-2 border rounded-full w-40 h-12 flex items-center justify-center z-50 mb-10 mr-10">
            <div class="mr-4 text-gray-700 dark:text-gray-300">Dark Mode</div>
            <div class="dark-mode-switcher__toggle border"></div>
        </div>
        <!-- END: Dark Mode Switcher-->
        <!-- BEGIN: JS Assets-->
        <script src="https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/markerclusterer.js"></script>
        <script src="https://maps.googleapis.com/maps/api/js?key=["your-google-map-api"]&libraries=places"></script>
        <script src="dist/js/app.js"></script>
        <!-- END: JS Assets-->
    </body>
</html>