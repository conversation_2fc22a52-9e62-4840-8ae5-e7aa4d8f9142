<!DOCTYPE html>
<!--
Template Name: Midone - HTML Admin Dashboard Template
Author: Left4code
Website: http://www.left4code.com/
Contact: muham<PERSON><PERSON><PERSON>@left4code.com
Purchase: https://themeforest.net/user/left4code/portfolio
Renew Support: https://themeforest.net/user/left4code/portfolio
License: You must have a valid license purchased only from themeforest(the above link) in order to legally use the theme for your project.
-->
<html lang="en" class="light">
    <!-- BEGIN: Head -->
    <head>
        <meta charset="utf-8">
        <link href="dist/images/logo.svg" rel="shortcut icon">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="Midone admin is super flexible, powerful, clean & modern responsive tailwind admin template with unlimited possibilities.">
        <meta name="keywords" content="admin template, Midone admin template, dashboard template, flat admin template, responsive admin template, web app">
        <meta name="author" content="LEFT4CODE">
        <title>Typography - Midone - Tailwind HTML Admin Template</title>
        <!-- BEGIN: CSS Assets-->
        <link rel="stylesheet" href="dist/css/app.css" />
        <!-- END: CSS Assets-->
    </head>
    <!-- END: Head -->
    <body class="app">
        <!-- BEGIN: Mobile Menu -->
        <div class="mobile-menu md:hidden">
            <div class="mobile-menu-bar">
                <a href="" class="flex mr-auto">
                    <img alt="Midone Tailwind HTML Admin Template" class="w-6" src="dist/images/logo.svg">
                </a>
                <a href="javascript:;" id="mobile-menu-toggler"> <i data-feather="bar-chart-2" class="w-8 h-8 text-white transform -rotate-90"></i> </a>
            </div>
            <ul class="border-t border-theme-24 py-5 hidden">
                <li>
                    <a href="index.html" class="menu">
                        <div class="menu__icon"> <i data-feather="home"></i> </div>
                        <div class="menu__title"> Dashboard </div>
                    </a>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="box"></i> </div>
                        <div class="menu__title"> Menu Layout <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="index.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Side Menu </div>
                            </a>
                        </li>
                        <li>
                            <a href="simple-menu-light-dashboard.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Simple Menu </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-dashboard.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Top Menu </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="side-menu-light-inbox.html" class="menu">
                        <div class="menu__icon"> <i data-feather="inbox"></i> </div>
                        <div class="menu__title"> Inbox </div>
                    </a>
                </li>
                <li>
                    <a href="side-menu-light-file-manager.html" class="menu">
                        <div class="menu__icon"> <i data-feather="hard-drive"></i> </div>
                        <div class="menu__title"> File Manager </div>
                    </a>
                </li>
                <li>
                    <a href="side-menu-light-point-of-sale.html" class="menu">
                        <div class="menu__icon"> <i data-feather="credit-card"></i> </div>
                        <div class="menu__title"> Point of Sale </div>
                    </a>
                </li>
                <li>
                    <a href="side-menu-light-chat.html" class="menu">
                        <div class="menu__icon"> <i data-feather="message-square"></i> </div>
                        <div class="menu__title"> Chat </div>
                    </a>
                </li>
                <li>
                    <a href="side-menu-light-post.html" class="menu">
                        <div class="menu__icon"> <i data-feather="file-text"></i> </div>
                        <div class="menu__title"> Post </div>
                    </a>
                </li>
                <li class="menu__devider my-6"></li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="edit"></i> </div>
                        <div class="menu__title"> Crud <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="side-menu-light-crud-data-list.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Data List </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-crud-form.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Form </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="users"></i> </div>
                        <div class="menu__title"> Users <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="side-menu-light-users-layout-1.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Layout 1 </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-users-layout-2.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Layout 2 </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-users-layout-3.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Layout 3 </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="trello"></i> </div>
                        <div class="menu__title"> Profile <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="side-menu-light-profile-overview-1.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Overview 1 </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-profile-overview-2.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Overview 2 </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-profile-overview-3.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Overview 3 </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="layout"></i> </div>
                        <div class="menu__title"> Pages <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Wizards <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-wizard-layout-1.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-wizard-layout-2.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-wizard-layout-3.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Blog <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-blog-layout-1.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-blog-layout-2.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-blog-layout-3.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Pricing <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-pricing-layout-1.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-pricing-layout-2.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 2</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Invoice <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-invoice-layout-1.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-invoice-layout-2.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 2</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> FAQ <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-faq-layout-1.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-faq-layout-2.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-faq-layout-3.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Layout 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="login-light-login.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Login </div>
                            </a>
                        </li>
                        <li>
                            <a href="login-light-register.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Register </div>
                            </a>
                        </li>
                        <li>
                            <a href="main-light-error-page.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Error Page </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-update-profile.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Update profile </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-change-password.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Change Password </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="menu__devider my-6"></li>
                <li>
                    <a href="javascript:;.html" class="menu menu--active">
                        <div class="menu__icon"> <i data-feather="inbox"></i> </div>
                        <div class="menu__title"> Components <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="menu__sub-open">
                        <li>
                            <a href="javascript:;" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Grid <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-regular-table.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Regular Table</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-tabulator.html" class="menu">
                                        <div class="menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="menu__title">Tabulator</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="side-menu-light-accordion.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Accordion </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-button.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Button </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-modal.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Modal </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-alert.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Alert </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-progress-bar.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Progress Bar </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-tooltip.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Tooltip </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-dropdown.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Dropdown </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-toast.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Toast </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-typography.html" class="menu menu--active">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Typography </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-icon.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Icon </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-loading-icon.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Loading Icon </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="sidebar"></i> </div>
                        <div class="menu__title"> Forms <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="side-menu-light-regular-form.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Regular Form </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-datepicker.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Datepicker </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-tail-select.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Tail Select </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-file-upload.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> File Upload </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-wysiwyg-editor.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Wysiwyg Editor </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-validation.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Validation </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="menu">
                        <div class="menu__icon"> <i data-feather="hard-drive"></i> </div>
                        <div class="menu__title"> Widgets <i data-feather="chevron-down" class="menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="side-menu-light-chart.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Chart </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-slider.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Slider </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-image-zoom.html" class="menu">
                                <div class="menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="menu__title"> Image Zoom </div>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
        <!-- END: Mobile Menu -->
        <!-- BEGIN: Top Bar -->
        <div class="border-b border-theme-24 -mt-10 md:-mt-5 -mx-3 sm:-mx-8 px-3 sm:px-8 pt-3 md:pt-0 mb-10">
            <div class="top-bar-boxed flex items-center">
                <!-- BEGIN: Logo -->
                <a href="" class="-intro-x hidden md:flex">
                    <img alt="Midone Tailwind HTML Admin Template" class="w-6" src="dist/images/logo.svg">
                    <span class="text-white text-lg ml-3"> Mid<span class="font-medium">one</span> </span>
                </a>
                <!-- END: Logo -->
                <!-- BEGIN: Breadcrumb -->
                <div class="-intro-x breadcrumb breadcrumb--light mr-auto"> <a href="" class="">Application</a> <i data-feather="chevron-right" class="breadcrumb__icon"></i> <a href="" class="breadcrumb--active">Dashboard</a> </div>
                <!-- END: Breadcrumb -->
                <!-- BEGIN: Search -->
                <div class="intro-x relative mr-3 sm:mr-6">
                    <div class="search hidden sm:block">
                        <input type="text" class="search__input input dark:bg-dark-1 placeholder-theme-13" placeholder="Search...">
                        <i data-feather="search" class="search__icon dark:text-gray-300"></i> 
                    </div>
                    <a class="notification notification--light sm:hidden" href=""> <i data-feather="search" class="notification__icon dark:text-gray-300"></i> </a>
                    <div class="search-result">
                        <div class="search-result__content">
                            <div class="search-result__content__title">Pages</div>
                            <div class="mb-5">
                                <a href="" class="flex items-center">
                                    <div class="w-8 h-8 bg-theme-18 text-theme-9 flex items-center justify-center rounded-full"> <i class="w-4 h-4" data-feather="inbox"></i> </div>
                                    <div class="ml-3">Mail Settings</div>
                                </a>
                                <a href="" class="flex items-center mt-2">
                                    <div class="w-8 h-8 bg-theme-17 text-theme-11 flex items-center justify-center rounded-full"> <i class="w-4 h-4" data-feather="users"></i> </div>
                                    <div class="ml-3">Users & Permissions</div>
                                </a>
                                <a href="" class="flex items-center mt-2">
                                    <div class="w-8 h-8 bg-theme-14 text-theme-10 flex items-center justify-center rounded-full"> <i class="w-4 h-4" data-feather="credit-card"></i> </div>
                                    <div class="ml-3">Transactions Report</div>
                                </a>
                            </div>
                            <div class="search-result__content__title">Users</div>
                            <div class="mb-5">
                                <a href="" class="flex items-center mt-2">
                                    <div class="w-8 h-8 image-fit">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-3.jpg">
                                    </div>
                                    <div class="ml-3">Keanu Reeves</div>
                                    <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right"><EMAIL></div>
                                </a>
                                <a href="" class="flex items-center mt-2">
                                    <div class="w-8 h-8 image-fit">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-4.jpg">
                                    </div>
                                    <div class="ml-3">Leonardo DiCaprio</div>
                                    <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right"><EMAIL></div>
                                </a>
                                <a href="" class="flex items-center mt-2">
                                    <div class="w-8 h-8 image-fit">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-2.jpg">
                                    </div>
                                    <div class="ml-3">Christian Bale</div>
                                    <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right"><EMAIL></div>
                                </a>
                                <a href="" class="flex items-center mt-2">
                                    <div class="w-8 h-8 image-fit">
                                        <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-12.jpg">
                                    </div>
                                    <div class="ml-3">Sean Connery</div>
                                    <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right"><EMAIL></div>
                                </a>
                            </div>
                            <div class="search-result__content__title">Products</div>
                            <a href="" class="flex items-center mt-2">
                                <div class="w-8 h-8 image-fit">
                                    <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/preview-8.jpg">
                                </div>
                                <div class="ml-3">Samsung Q90 QLED TV</div>
                                <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right">Electronic</div>
                            </a>
                            <a href="" class="flex items-center mt-2">
                                <div class="w-8 h-8 image-fit">
                                    <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/preview-7.jpg">
                                </div>
                                <div class="ml-3">Apple MacBook Pro 13</div>
                                <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right">PC &amp; Laptop</div>
                            </a>
                            <a href="" class="flex items-center mt-2">
                                <div class="w-8 h-8 image-fit">
                                    <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/preview-15.jpg">
                                </div>
                                <div class="ml-3">Sony Master Series A9G</div>
                                <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right">Electronic</div>
                            </a>
                            <a href="" class="flex items-center mt-2">
                                <div class="w-8 h-8 image-fit">
                                    <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/preview-6.jpg">
                                </div>
                                <div class="ml-3">Nike Tanjun</div>
                                <div class="ml-auto w-48 truncate text-gray-600 text-xs text-right">Sport &amp; Outdoor</div>
                            </a>
                        </div>
                    </div>
                </div>
                <!-- END: Search -->
                <!-- BEGIN: Notifications -->
                <div class="intro-x dropdown mr-4 sm:mr-6">
                    <div class="dropdown-toggle notification notification--light notification--bullet cursor-pointer"> <i data-feather="bell" class="notification__icon dark:text-gray-300"></i> </div>
                    <div class="notification-content pt-2 dropdown-box">
                        <div class="notification-content__box dropdown-box__content box dark:bg-dark-6">
                            <div class="notification-content__title">Notifications</div>
                            <div class="cursor-pointer relative flex items-center ">
                                <div class="w-12 h-12 flex-none image-fit mr-1">
                                    <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-3.jpg">
                                    <div class="w-3 h-3 bg-theme-9 absolute right-0 bottom-0 rounded-full border-2 border-white"></div>
                                </div>
                                <div class="ml-2 overflow-hidden">
                                    <div class="flex items-center">
                                        <a href="javascript:;" class="font-medium truncate mr-5">Keanu Reeves</a> 
                                        <div class="text-xs text-gray-500 ml-auto whitespace-no-wrap">03:20 PM</div>
                                    </div>
                                    <div class="w-full truncate text-gray-600">Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry&#039;s standard dummy text ever since the 1500</div>
                                </div>
                            </div>
                            <div class="cursor-pointer relative flex items-center mt-5">
                                <div class="w-12 h-12 flex-none image-fit mr-1">
                                    <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-4.jpg">
                                    <div class="w-3 h-3 bg-theme-9 absolute right-0 bottom-0 rounded-full border-2 border-white"></div>
                                </div>
                                <div class="ml-2 overflow-hidden">
                                    <div class="flex items-center">
                                        <a href="javascript:;" class="font-medium truncate mr-5">Leonardo DiCaprio</a> 
                                        <div class="text-xs text-gray-500 ml-auto whitespace-no-wrap">06:05 AM</div>
                                    </div>
                                    <div class="w-full truncate text-gray-600">There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomi</div>
                                </div>
                            </div>
                            <div class="cursor-pointer relative flex items-center mt-5">
                                <div class="w-12 h-12 flex-none image-fit mr-1">
                                    <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-2.jpg">
                                    <div class="w-3 h-3 bg-theme-9 absolute right-0 bottom-0 rounded-full border-2 border-white"></div>
                                </div>
                                <div class="ml-2 overflow-hidden">
                                    <div class="flex items-center">
                                        <a href="javascript:;" class="font-medium truncate mr-5">Christian Bale</a> 
                                        <div class="text-xs text-gray-500 ml-auto whitespace-no-wrap">01:10 PM</div>
                                    </div>
                                    <div class="w-full truncate text-gray-600">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem </div>
                                </div>
                            </div>
                            <div class="cursor-pointer relative flex items-center mt-5">
                                <div class="w-12 h-12 flex-none image-fit mr-1">
                                    <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-12.jpg">
                                    <div class="w-3 h-3 bg-theme-9 absolute right-0 bottom-0 rounded-full border-2 border-white"></div>
                                </div>
                                <div class="ml-2 overflow-hidden">
                                    <div class="flex items-center">
                                        <a href="javascript:;" class="font-medium truncate mr-5">Sean Connery</a> 
                                        <div class="text-xs text-gray-500 ml-auto whitespace-no-wrap">05:09 AM</div>
                                    </div>
                                    <div class="w-full truncate text-gray-600">There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomi</div>
                                </div>
                            </div>
                            <div class="cursor-pointer relative flex items-center mt-5">
                                <div class="w-12 h-12 flex-none image-fit mr-1">
                                    <img alt="Midone Tailwind HTML Admin Template" class="rounded-full" src="dist/images/profile-1.jpg">
                                    <div class="w-3 h-3 bg-theme-9 absolute right-0 bottom-0 rounded-full border-2 border-white"></div>
                                </div>
                                <div class="ml-2 overflow-hidden">
                                    <div class="flex items-center">
                                        <a href="javascript:;" class="font-medium truncate mr-5">Leonardo DiCaprio</a> 
                                        <div class="text-xs text-gray-500 ml-auto whitespace-no-wrap">01:10 PM</div>
                                    </div>
                                    <div class="w-full truncate text-gray-600">There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomi</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END: Notifications -->
                <!-- BEGIN: Account Menu -->
                <div class="intro-x dropdown w-8 h-8">
                    <div class="dropdown-toggle w-8 h-8 rounded-full overflow-hidden shadow-lg image-fit zoom-in scale-110">
                        <img alt="Midone Tailwind HTML Admin Template" src="dist/images/profile-2.jpg">
                    </div>
                    <div class="dropdown-box w-56">
                        <div class="dropdown-box__content box bg-theme-38 dark:bg-dark-6 text-white">
                            <div class="p-4 border-b border-theme-40 dark:border-dark-3">
                                <div class="font-medium">Keanu Reeves</div>
                                <div class="text-xs text-theme-41 dark:text-gray-600">DevOps Engineer</div>
                            </div>
                            <div class="p-2">
                                <a href="" class="flex items-center block p-2 transition duration-300 ease-in-out hover:bg-theme-1 dark:hover:bg-dark-3 rounded-md"> <i data-feather="user" class="w-4 h-4 mr-2"></i> Profile </a>
                                <a href="" class="flex items-center block p-2 transition duration-300 ease-in-out hover:bg-theme-1 dark:hover:bg-dark-3 rounded-md"> <i data-feather="edit" class="w-4 h-4 mr-2"></i> Add Account </a>
                                <a href="" class="flex items-center block p-2 transition duration-300 ease-in-out hover:bg-theme-1 dark:hover:bg-dark-3 rounded-md"> <i data-feather="lock" class="w-4 h-4 mr-2"></i> Reset Password </a>
                                <a href="" class="flex items-center block p-2 transition duration-300 ease-in-out hover:bg-theme-1 dark:hover:bg-dark-3 rounded-md"> <i data-feather="help-circle" class="w-4 h-4 mr-2"></i> Help </a>
                            </div>
                            <div class="p-2 border-t border-theme-40 dark:border-dark-3">
                                <a href="" class="flex items-center block p-2 transition duration-300 ease-in-out hover:bg-theme-1 dark:hover:bg-dark-3 rounded-md"> <i data-feather="toggle-right" class="w-4 h-4 mr-2"></i> Logout </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END: Account Menu -->
            </div>
        </div>
        <!-- END: Top Bar -->
        <!-- BEGIN: Top Menu -->
        <nav class="top-nav">
            <ul>
                <li>
                    <a href="top-menu-light-dashboard.html" class="top-menu">
                        <div class="top-menu__icon"> <i data-feather="home"></i> </div>
                        <div class="top-menu__title"> Dashboard </div>
                    </a>
                </li>
                <li>
                    <a href="javascript:;" class="top-menu">
                        <div class="top-menu__icon"> <i data-feather="box"></i> </div>
                        <div class="top-menu__title"> Menu Layout <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="index.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Side Menu </div>
                            </a>
                        </li>
                        <li>
                            <a href="simple-menu-light-dashboard.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Simple Menu </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-dashboard.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Top Menu </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="top-menu">
                        <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                        <div class="top-menu__title"> Apps <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="javascript:;" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Users <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="top-menu-light-users-layout-1.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-users-layout-2.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-users-layout-3.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Profile <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="top-menu-light-profile-overview-1.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Overview 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-profile-overview-2.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Overview 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-profile-overview-3.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Overview 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="top-menu-light-inbox.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Inbox </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-file-manager.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> File Manager </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-point-of-sale.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Point of Sale </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-chat.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Chat </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-post.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Post </div>
                            </a>
                        </li>
                        <li>
                            <a href="javascript:;" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Crud <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="side-menu-light-crud-data-list.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Data List</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="side-menu-light-crud-form.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Form</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="top-menu">
                        <div class="top-menu__icon"> <i data-feather="layout"></i> </div>
                        <div class="top-menu__title"> Pages <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="javascript:;" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Wizards <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="top-menu-light-wizard-layout-1.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-wizard-layout-2.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-wizard-layout-3.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Blog <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="top-menu-light-blog-layout-1.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-blog-layout-2.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-blog-layout-3.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Pricing <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="top-menu-light-pricing-layout-1.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-pricing-layout-2.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 2</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Invoice <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="top-menu-light-invoice-layout-1.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-invoice-layout-2.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 2</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="javascript:;" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> FAQ <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="top-menu-light-faq-layout-1.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 1</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-faq-layout-2.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 2</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-faq-layout-3.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Layout 3</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="login-light-login.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Login </div>
                            </a>
                        </li>
                        <li>
                            <a href="login-light-register.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Register </div>
                            </a>
                        </li>
                        <li>
                            <a href="main-light-error-page.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Error Page </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-update-profile.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Update profile </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-change-password.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Change Password </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;.html" class="top-menu top-menu--active">
                        <div class="top-menu__icon"> <i data-feather="inbox"></i> </div>
                        <div class="top-menu__title"> Components <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                    </a>
                    <ul class="top-menu__sub-open">
                        <li>
                            <a href="javascript:;" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Grid <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                            </a>
                            <ul class="">
                                <li>
                                    <a href="top-menu-light-regular-table.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Regular Table</div>
                                    </a>
                                </li>
                                <li>
                                    <a href="top-menu-light-tabulator.html" class="top-menu">
                                        <div class="top-menu__icon"> <i data-feather="zap"></i> </div>
                                        <div class="top-menu__title">Tabulator</div>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="top-menu-light-accordion.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Accordion </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-button.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Button </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-modal.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Modal </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-alert.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Alert </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-progress-bar.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Progress Bar </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-tooltip.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Tooltip </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-dropdown.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Dropdown </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-toast.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Toast </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-typography.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Typography </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-icon.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Icon </div>
                            </a>
                        </li>
                        <li>
                            <a href="side-menu-light-loading-icon.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Loading Icon </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="top-menu">
                        <div class="top-menu__icon"> <i data-feather="sidebar"></i> </div>
                        <div class="top-menu__title"> Forms <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="top-menu-light-regular-form.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Regular Form </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-datepicker.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Datepicker </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-tail-select.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Tail Select </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-file-upload.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> File Upload </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-wysiwyg-editor.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Wysiwyg Editor </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-validation.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Validation </div>
                            </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="javascript:;" class="top-menu">
                        <div class="top-menu__icon"> <i data-feather="hard-drive"></i> </div>
                        <div class="top-menu__title"> Widgets <i data-feather="chevron-down" class="top-menu__sub-icon"></i> </div>
                    </a>
                    <ul class="">
                        <li>
                            <a href="top-menu-light-chart.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Chart </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-slider.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Slider </div>
                            </a>
                        </li>
                        <li>
                            <a href="top-menu-light-image-zoom.html" class="top-menu">
                                <div class="top-menu__icon"> <i data-feather="activity"></i> </div>
                                <div class="top-menu__title"> Image Zoom </div>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </nav>
        <!-- END: Top Menu -->
        <!-- BEGIN: Content -->
        <div class="content">
            <div class="intro-y flex items-center mt-8">
                <h2 class="text-lg font-medium mr-auto">
                    Typography
                </h2>
            </div>
            <div class="grid grid-cols-12 gap-6 mt-5">
                <div class="intro-y col-span-12 lg:col-span-6">
                    <!-- BEGIN: HEADING -->
                    <div class="intro-y box">
                        <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                            <h2 class="font-medium text-base mr-auto">
                                Heading
                            </h2>
                            <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                <div class="mr-3">Show example code</div>
                                <input data-target="#heading" class="show-code input input--switch border" type="checkbox">
                            </div>
                        </div>
                        <div class="p-5" id="heading">
                            <div class="preview">
                                <div>
                                    <h1 class="text-4xl font-medium leading-none">
                                        h1. Heading 1
                                    </h1>
                                    <h2 class="text-3xl font-medium leading-none mt-3">
                                        h2. Heading 2
                                    </h2>
                                    <h3 class="text-2xl font-medium leading-none mt-3">
                                        h3. Heading 3
                                    </h3>
                                    <h4 class="text-xl font-medium leading-none mt-3">
                                        h4. Heading 4
                                    </h4>
                                    <h5 class="text-lg font-medium leading-none mt-3">
                                        h5. Heading 5
                                    </h5>
                                    <h6 class="font-medium leading-none mt-3">
                                        h6. Heading 6
                                    </h6>
                                </div>
                                <div class="mt-5">
                                    <h1 class="text-4xl text-theme-1 font-medium leading-none">
                                        h1. Heading 1
                                    </h1>
                                    <h2 class="text-3xl text-gray-700 dark:text-gray-600 font-medium leading-none mt-3">
                                        h2. Heading 2
                                    </h2>
                                    <h3 class="text-2xl text-theme-9 font-medium leading-none mt-3">
                                        h3. Heading 3
                                    </h3>
                                    <h4 class="text-xl text-theme-12 font-medium leading-none mt-3">
                                        h4. Heading 4
                                    </h4>
                                    <h5 class="text-lg text-theme-6 font-medium leading-none mt-3">
                                        h5. Heading 5
                                    </h5>
                                    <h6 class="text-gray-600 font-medium leading-none mt-3">
                                        h6. Heading 6
                                    </h6>
                                </div>
                            </div>
                            <div class="source-code hidden">
                                <button data-target="#copy-heading" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                <div class="overflow-y-auto h-64 mt-3">
                                    <pre class="source-preview" id="copy-heading"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagdivHTMLCloseTag HTMLOpenTagh1 class=&quot;text-4xl font-medium leading-none&quot;HTMLCloseTagh1. Heading 1HTMLOpenTag/h1HTMLCloseTag HTMLOpenTagh2 class=&quot;text-3xl font-medium leading-none mt-3&quot;HTMLCloseTagh2. Heading 2HTMLOpenTag/h2HTMLCloseTag HTMLOpenTagh3 class=&quot;text-2xl font-medium leading-none mt-3&quot;HTMLCloseTagh3. Heading 3HTMLOpenTag/h3HTMLCloseTag HTMLOpenTagh4 class=&quot;text-xl font-medium leading-none mt-3&quot;HTMLCloseTagh4. Heading 4HTMLOpenTag/h4HTMLCloseTag HTMLOpenTagh5 class=&quot;text-lg font-medium leading-none mt-3&quot;HTMLCloseTagh5. Heading 5HTMLOpenTag/h5HTMLCloseTag HTMLOpenTagh6 class=&quot;font-medium leading-none mt-3&quot;HTMLCloseTagh6. Heading 6HTMLOpenTag/h6HTMLCloseTag HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-5&quot;HTMLCloseTag HTMLOpenTagh1 class=&quot;text-4xl text-theme-1 font-medium leading-none&quot;HTMLCloseTagh1. Heading 1HTMLOpenTag/h1HTMLCloseTag HTMLOpenTagh2 class=&quot;text-3xl text-gray-700 dark:text-gray-600 font-medium leading-none mt-3&quot;HTMLCloseTagh2. Heading 2HTMLOpenTag/h2HTMLCloseTag HTMLOpenTagh3 class=&quot;text-2xl text-theme-9 font-medium leading-none mt-3&quot;HTMLCloseTagh3. Heading 3HTMLOpenTag/h3HTMLCloseTag HTMLOpenTagh4 class=&quot;text-xl text-theme-12 font-medium leading-none mt-3&quot;HTMLCloseTagh4. Heading 4HTMLOpenTag/h4HTMLCloseTag HTMLOpenTagh5 class=&quot;text-lg text-theme-6 font-medium leading-none mt-3&quot;HTMLCloseTagh5. Heading 5HTMLOpenTag/h5HTMLCloseTag HTMLOpenTagh6 class=&quot;text-gray-600 font-medium leading-none mt-3&quot;HTMLCloseTagh6. Heading 6HTMLOpenTag/h6HTMLCloseTag HTMLOpenTag/divHTMLCloseTag </code> </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: HEADING -->
                    <!-- BEGIN: TEXT SETTINGS -->
                    <div class="intro-y box mt-5">
                        <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                            <h2 class="font-medium text-base mr-auto">
                                Text Settings
                            </h2>
                            <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                <div class="mr-3">Show example code</div>
                                <input data-target="#text-settings" class="show-code input input--switch border" type="checkbox">
                            </div>
                        </div>
                        <div class="p-5" id="text-settings">
                            <div class="preview">
                                <div>
                                    <div class="font-normal">Example text</div>
                                    <div class="font-medium">Example medium text</div>
                                    <div class="font-semibold">Example semibold text</div>
                                    <div class="font-bold">Example bolder text</div>
                                    <div class="font-extrabold">Example boldest text</div>
                                </div>
                                <div class="mt-5">
                                    <div class="uppercase">Example uppercase text</div>
                                    <div class="lowercase">Example lowercase text</div>
                                    <div class="capitalize">Example capitalized text</div>
                                    <div class="normal-case">Example cursive text</div>
                                </div>
                            </div>
                            <div class="source-code hidden">
                                <button data-target="#copy-text-settings" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                <div class="overflow-y-auto h-64 mt-3">
                                    <pre class="source-preview" id="copy-text-settings"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagdivHTMLCloseTag HTMLOpenTagdiv class=&quot;font-normal&quot;HTMLCloseTagExample textHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;font-medium&quot;HTMLCloseTagExample medium textHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;font-semibold&quot;HTMLCloseTagExample semibold textHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;font-bold&quot;HTMLCloseTagExample bolder textHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;font-extrabold&quot;HTMLCloseTagExample boldest textHTMLOpenTag/divHTMLCloseTag HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-5&quot;HTMLCloseTag HTMLOpenTagdiv class=&quot;uppercase&quot;HTMLCloseTagExample uppercase textHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;lowercase&quot;HTMLCloseTagExample lowercase textHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;capitalize&quot;HTMLCloseTagExample capitalized textHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;normal-case&quot;HTMLCloseTagExample cursive textHTMLOpenTag/divHTMLCloseTag HTMLOpenTag/divHTMLCloseTag </code> </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: TEXT SETTINGS -->
                    <!-- BEGIN: COMMON ELEMENTS -->
                    <div class="intro-y box mt-5">
                        <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                            <h2 class="font-medium text-base mr-auto">
                                Common Elements
                            </h2>
                            <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                <div class="mr-3">Show example code</div>
                                <input data-target="#common-elements" class="show-code input input--switch border" type="checkbox">
                            </div>
                        </div>
                        <div class="p-5" id="common-elements">
                            <div class="preview">
                                <div class="">
                                    You can use the mark tag to 
                                    <mark class="p-1 bg-yellow-200">highlight</mark>
                                    text.
                                </div>
                                <del class="block mt-1">This line of text is meant to be treated as deleted text.</del>
                                <s class="block mt-1">This line of text is meant to be treated as no longer accurate.</s>
                                <ins class="block mt-1">This line of text is meant to be treated as an addition to the document.</ins>
                                <u class="block mt-1">This line of text will render as underlined</u>
                                <small class="block mt-1">This line of text is meant to be treated as fine print.</small> <strong class="block mt-1">This line rendered as bold text.</strong> <em class="block mt-1">This line rendered as italicized text.</em> 
                            </div>
                            <div class="source-code hidden">
                                <button data-target="#copy-common-elements" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                <div class="overflow-y-auto h-64 mt-3">
                                    <pre class="source-preview" id="copy-common-elements"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagdiv class=&quot;&quot;HTMLCloseTagYou can use the mark tag to HTMLOpenTagmark class=&quot;p-1 bg-yellow-200&quot;HTMLCloseTaghighlightHTMLOpenTag/markHTMLCloseTag text.HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdel class=&quot;block mt-1&quot;HTMLCloseTagThis line of text is meant to be treated as deleted text.HTMLOpenTag/delHTMLCloseTag HTMLOpenTags class=&quot;block mt-1&quot;HTMLCloseTagThis line of text is meant to be treated as no longer accurate.HTMLOpenTag/sHTMLCloseTag HTMLOpenTagins class=&quot;block mt-1&quot;HTMLCloseTagThis line of text is meant to be treated as an addition to the document.HTMLOpenTag/insHTMLCloseTag HTMLOpenTagu class=&quot;block mt-1&quot;HTMLCloseTagThis line of text will render as underlinedHTMLOpenTag/uHTMLCloseTag HTMLOpenTagsmall class=&quot;block mt-1&quot;HTMLCloseTagThis line of text is meant to be treated as fine print.HTMLOpenTag/smallHTMLCloseTag HTMLOpenTagstrong class=&quot;block mt-1&quot;HTMLCloseTagThis line rendered as bold text.HTMLOpenTag/strongHTMLCloseTag HTMLOpenTagem class=&quot;block mt-1&quot;HTMLCloseTagThis line rendered as italicized text.HTMLOpenTag/emHTMLCloseTag </code> </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: COMMON ELEMENTS -->
                </div>
                <div class="intro-y col-span-12 lg:col-span-6">
                    <!-- BEGIN: BADGES -->
                    <div class="intro-y box">
                        <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                            <h2 class="font-medium text-base mr-auto">
                                Badges
                            </h2>
                            <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                <div class="mr-3">Show example code</div>
                                <input data-target="#badge" class="show-code input input--switch border" type="checkbox">
                            </div>
                        </div>
                        <div class="p-5" id="badge">
                            <div class="preview">
                                <div class="font-medium">Basic Badge</div>
                                <div class="mt-2"> <span class="text-xs px-1 rounded-full bg-theme-1 text-white mr-1">1</span> <span class="text-xs px-1 rounded-full border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1">2</span> <span class="text-xs px-1 rounded-full bg-theme-9 text-white mr-1">3</span> <span class="text-xs px-1 rounded-full bg-theme-12 text-white mr-1">4</span> <span class="text-xs px-1 rounded-full bg-theme-6 text-white mr-1">5</span> <span class="text-xs px-1 rounded-full bg-gray-200 text-gray-600 mr-1">6</span> </div>
                                <div class="font-medium mt-6">Badge Sizes</div>
                                <div class="mt-3"> <span class="px-2 py-1 rounded-full bg-theme-1 text-white mr-1">1</span> <span class="px-2 py-1 rounded-full border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1">2</span> <span class="px-2 py-1 rounded-full bg-theme-9 text-white mr-1">3</span> <span class="px-2 py-1 rounded-full bg-theme-12 text-white mr-1">4</span> <span class="px-2 py-1 rounded-full bg-theme-6 text-white mr-1">5</span> <span class="px-2 py-1 rounded-full bg-gray-200 text-gray-600 mr-1">6</span> </div>
                                <div class="mt-4"> <span class="px-3 py-2 rounded-full bg-theme-1 text-white mr-1">1</span> <span class="px-3 py-2 rounded-full border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1">2</span> <span class="px-3 py-2 rounded-full bg-theme-9 text-white mr-1">3</span> <span class="px-3 py-2 rounded-full bg-theme-12 text-white mr-1">4</span> <span class="px-3 py-2 rounded-full bg-theme-6 text-white mr-1">5</span> <span class="px-3 py-2 rounded-full bg-gray-200 text-gray-600 mr-1">6</span> </div>
                                <div class="mt-6"> <span class="px-4 py-3 rounded-full bg-theme-1 text-white mr-1">1</span> <span class="px-4 py-3 rounded-full border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1">2</span> <span class="px-4 py-3 rounded-full bg-theme-9 text-white mr-1">3</span> <span class="px-4 py-3 rounded-full bg-theme-12 text-white mr-1">4</span> <span class="px-4 py-3 rounded-full bg-theme-6 text-white mr-1">5</span> <span class="px-4 py-3 rounded-full bg-gray-200 text-gray-600 mr-1">6</span> </div>
                                <div class="font-medium mt-10">Square Badge</div>
                                <div class="mt-2"> <span class="text-xs px-1 bg-theme-1 text-white mr-1">1</span> <span class="text-xs px-1 border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1">2</span> <span class="text-xs px-1 bg-theme-9 text-white mr-1">3</span> <span class="text-xs px-1 bg-theme-12 text-white mr-1">4</span> <span class="text-xs px-1 bg-theme-6 text-white mr-1">5</span> <span class="text-xs px-1 bg-gray-200 text-gray-600 mr-1">6</span> </div>
                                <div class="font-medium mt-6">Outline Badge</div>
                                <div class="mt-4"> <span class="px-3 py-2 rounded-full border border-theme-1 text-theme-1 dark:text-theme-10 dark:border-theme-10 mr-1">1</span> <span class="px-3 py-2 rounded-full border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1">2</span> <span class="px-3 py-2 rounded-full border border-theme-9 text-theme-9 dark:border-theme-9 mr-1">3</span> <span class="px-3 py-2 rounded-full border border-theme-12 text-theme-12 dark:border-theme-12 mr-1">4</span> <span class="px-3 py-2 rounded-full border border-theme-6 text-theme-6 dark:border-theme-6 mr-1">5</span> </div>
                            </div>
                            <div class="source-code hidden">
                                <button data-target="#copy-badge" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                <div class="overflow-y-auto h-64 mt-3">
                                    <pre class="source-preview" id="copy-badge"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagdiv class=&quot;font-medium&quot;HTMLCloseTagBasic BadgeHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-2&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 rounded-full bg-theme-1 text-white mr-1&quot;HTMLCloseTag1HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 rounded-full border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1&quot;HTMLCloseTag2HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 rounded-full bg-theme-9 text-white mr-1&quot;HTMLCloseTag3HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 rounded-full bg-theme-12 text-white mr-1&quot;HTMLCloseTag4HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 rounded-full bg-theme-6 text-white mr-1&quot;HTMLCloseTag5HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 rounded-full bg-gray-200 text-gray-600 mr-1&quot;HTMLCloseTag6HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;font-medium mt-6&quot;HTMLCloseTagBadge SizesHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-3&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;px-2 py-1 rounded-full bg-theme-1 text-white mr-1&quot;HTMLCloseTag1HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-2 py-1 rounded-full border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1&quot;HTMLCloseTag2HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-2 py-1 rounded-full bg-theme-9 text-white mr-1&quot;HTMLCloseTag3HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-2 py-1 rounded-full bg-theme-12 text-white mr-1&quot;HTMLCloseTag4HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-2 py-1 rounded-full bg-theme-6 text-white mr-1&quot;HTMLCloseTag5HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-2 py-1 rounded-full bg-gray-200 text-gray-600 mr-1&quot;HTMLCloseTag6HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-4&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full bg-theme-1 text-white mr-1&quot;HTMLCloseTag1HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1&quot;HTMLCloseTag2HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full bg-theme-9 text-white mr-1&quot;HTMLCloseTag3HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full bg-theme-12 text-white mr-1&quot;HTMLCloseTag4HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full bg-theme-6 text-white mr-1&quot;HTMLCloseTag5HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full bg-gray-200 text-gray-600 mr-1&quot;HTMLCloseTag6HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-6&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;px-4 py-3 rounded-full bg-theme-1 text-white mr-1&quot;HTMLCloseTag1HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-4 py-3 rounded-full border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1&quot;HTMLCloseTag2HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-4 py-3 rounded-full bg-theme-9 text-white mr-1&quot;HTMLCloseTag3HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-4 py-3 rounded-full bg-theme-12 text-white mr-1&quot;HTMLCloseTag4HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-4 py-3 rounded-full bg-theme-6 text-white mr-1&quot;HTMLCloseTag5HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-4 py-3 rounded-full bg-gray-200 text-gray-600 mr-1&quot;HTMLCloseTag6HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;font-medium mt-10&quot;HTMLCloseTagSquare BadgeHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-2&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 bg-theme-1 text-white mr-1&quot;HTMLCloseTag1HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1&quot;HTMLCloseTag2HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 bg-theme-9 text-white mr-1&quot;HTMLCloseTag3HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 bg-theme-12 text-white mr-1&quot;HTMLCloseTag4HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 bg-theme-6 text-white mr-1&quot;HTMLCloseTag5HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;text-xs px-1 bg-gray-200 text-gray-600 mr-1&quot;HTMLCloseTag6HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;font-medium mt-6&quot;HTMLCloseTagOutline BadgeHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-4&quot;HTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full border border-theme-1 text-theme-1 dark:text-theme-10 dark:border-theme-10 mr-1&quot;HTMLCloseTag1HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full border text-gray-700 dark:text-gray-600 dark:border-dark-5 mr-1&quot;HTMLCloseTag2HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full border border-theme-9 text-theme-9 dark:border-theme-9 mr-1&quot;HTMLCloseTag3HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full border border-theme-12 text-theme-12 dark:border-theme-12 mr-1&quot;HTMLCloseTag4HTMLOpenTag/spanHTMLCloseTag HTMLOpenTagspan class=&quot;px-3 py-2 rounded-full border border-theme-6 text-theme-6 dark:border-theme-6 mr-1&quot;HTMLCloseTag5HTMLOpenTag/spanHTMLCloseTag HTMLOpenTag/divHTMLCloseTag </code> </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: BADGES -->
                    <!-- BEGIN: SEPARATOR -->
                    <div class="intro-y box mt-5">
                        <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                            <h2 class="font-medium text-base mr-auto">
                                Separator
                            </h2>
                            <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                <div class="mr-3">Show example code</div>
                                <input data-target="#separator" class="show-code input input--switch border" type="checkbox">
                            </div>
                        </div>
                        <div class="p-5" id="separator">
                            <div class="preview">
                                <div class="w-full border-t border-gray-200 dark:border-dark-5 border-dashed"></div>
                                <div class="w-full border-t border-gray-200 dark:border-dark-5 mt-5"></div>
                            </div>
                            <div class="source-code hidden">
                                <button data-target="#copy-separator" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                <div class="overflow-y-auto h-64 mt-3">
                                    <pre class="source-preview" id="copy-separator"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagdiv class=&quot;w-full border-t border-gray-200 dark:border-dark-5 border-dashed&quot;HTMLCloseTagHTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;w-full border-t border-gray-200 dark:border-dark-5 mt-5&quot;HTMLCloseTagHTMLOpenTag/divHTMLCloseTag </code> </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: SEPARATOR -->
                    <!-- BEGIN: Devider -->
                    <div class="intro-y box mt-5">
                        <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                            <h2 class="font-medium text-base mr-auto">
                                Devider
                            </h2>
                            <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                <div class="mr-3">Show example code</div>
                                <input data-target="#devider" class="show-code input input--switch border" type="checkbox">
                            </div>
                        </div>
                        <div class="p-5" id="devider">
                            <div class="preview">
                                <div class="w-full flex justify-center border-t border-gray-200 dark:border-dark-5 mt-2">
                                    <div class="bg-white dark:bg-dark-3 px-5 -mt-3 text-gray-600">or</div>
                                </div>
                            </div>
                            <div class="source-code hidden">
                                <button data-target="#copy-devider" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                <div class="overflow-y-auto h-64 mt-3">
                                    <pre class="source-preview" id="copy-devider"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagdiv class=&quot;w-full flex justify-center border-t border-gray-200 dark:border-dark-5 mt-2&quot;HTMLCloseTag HTMLOpenTagdiv class=&quot;bg-white dark:bg-dark-3 px-5 -mt-3 text-gray-600&quot;HTMLCloseTagorHTMLOpenTag/divHTMLCloseTag HTMLOpenTag/divHTMLCloseTag </code> </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: Devider -->
                    <!-- BEGIN: Links -->
                    <div class="intro-y box mt-5">
                        <div class="flex flex-col sm:flex-row items-center p-5 border-b border-gray-200 dark:border-dark-5">
                            <h2 class="font-medium text-base mr-auto">
                                Links
                            </h2>
                            <div class="w-full sm:w-auto flex items-center sm:ml-auto mt-3 sm:mt-0">
                                <div class="mr-3">Show example code</div>
                                <input data-target="#link" class="show-code input input--switch border" type="checkbox">
                            </div>
                        </div>
                        <div class="p-5" id="link">
                            <div class="preview">
                                <div> <a href="" class="text-theme-1 block font-normal">Example text</a> <a href="" class="text-theme-1 block font-medium">Example medium text</a> <a href="" class="text-theme-1 block font-semibold">Example semibold text</a> <a href="" class="text-theme-1 block font-bold">Example bolder text</a> <a href="" class="text-theme-1 block font-extrabold">Example boldest text</a> </div>
                                <div class="mt-5"> <a href="" class="text-theme-1 block">Primary state</a> <a href="" class="text-gray-700 dark:text-gray-600 block">Secondary state</a> <a href="" class="text-theme-9 block">Success state</a> <a href="" class="text-theme-12 block">Warning state</a> <a href="" class="text-theme-6 block">Danger state</a> </div>
                            </div>
                            <div class="source-code hidden">
                                <button data-target="#copy-link" class="copy-code button button--sm border flex items-center text-gray-700 dark:border-dark-5 dark:text-gray-300"> <i data-feather="file" class="w-4 h-4 mr-2"></i> Copy example code </button>
                                <div class="overflow-y-auto h-64 mt-3">
                                    <pre class="source-preview" id="copy-link"> <code class="text-xs p-0 rounded-md html pl-5 pt-8 pb-4 -mb-10 -mt-10"> HTMLOpenTagdivHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;text-theme-1 block font-normal&quot;HTMLCloseTagExample textHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;text-theme-1 block font-medium&quot;HTMLCloseTagExample medium textHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;text-theme-1 block font-semibold&quot;HTMLCloseTagExample semibold textHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;text-theme-1 block font-bold&quot;HTMLCloseTagExample bolder textHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;text-theme-1 block font-extrabold&quot;HTMLCloseTagExample boldest textHTMLOpenTag/aHTMLCloseTag HTMLOpenTag/divHTMLCloseTag HTMLOpenTagdiv class=&quot;mt-5&quot;HTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;text-theme-1 block&quot;HTMLCloseTagPrimary stateHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;text-gray-700 dark:text-gray-600 block&quot;HTMLCloseTagSecondary stateHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;text-theme-9 block&quot;HTMLCloseTagSuccess stateHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;text-theme-12 block&quot;HTMLCloseTagWarning stateHTMLOpenTag/aHTMLCloseTag HTMLOpenTaga href=&quot;&quot; class=&quot;text-theme-6 block&quot;HTMLCloseTagDanger stateHTMLOpenTag/aHTMLCloseTag HTMLOpenTag/divHTMLCloseTag </code> </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END: Links -->
                </div>
            </div>
        </div>
        <!-- END: Content -->
        <!-- BEGIN: Dark Mode Switcher-->
        <div data-url="top-menu-dark-typography.html" class="dark-mode-switcher cursor-pointer shadow-md fixed bottom-0 right-0 box dark:bg-dark-2 border rounded-full w-40 h-12 flex items-center justify-center z-50 mb-10 mr-10">
            <div class="mr-4 text-gray-700 dark:text-gray-300">Dark Mode</div>
            <div class="dark-mode-switcher__toggle border"></div>
        </div>
        <!-- END: Dark Mode Switcher-->
        <!-- BEGIN: JS Assets-->
        <script src="https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/markerclusterer.js"></script>
        <script src="https://maps.googleapis.com/maps/api/js?key=["your-google-map-api"]&libraries=places"></script>
        <script src="dist/js/app.js"></script>
        <!-- END: JS Assets-->
    </body>
</html>