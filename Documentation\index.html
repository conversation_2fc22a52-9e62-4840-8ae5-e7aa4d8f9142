<!DOCTYPE html>
<!--
Template Name: Midone HTML Admin Template
Author: Left4code
Website: http://www.left4code.com/
Contact: muham<PERSON><PERSON><PERSON>@left4code.com
Purchase: https://themeforest.net/user/left4code/portfolio
Renew Support: https://themeforest.net/user/left4code/portfolio
License: You must have a valid license purchased only from themeforest(the above link) in order to legally use the theme for your project.
-->
<html lang="en">
    <!-- BEGIN: Head -->
    <head>
        <meta charset="utf-8">
        <link href="http://localhost/dist/images/logo.svg" rel="shortcut icon">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="Midone admin is super flexible, powerful, clean & modern responsive tailwind admin template with unlimited possibilities.">
        <meta name="keywords" content="admin template, Midone admin template, dashboard template, flat admin template, responsive admin template, web app">
        <meta name="author" content="LEFT4CODE">
        <title>Getting Started - Midone - Tailwind HTML Admin Template</title>
        <!-- BEGIN: CSS Assets-->
        <link rel="stylesheet" href="dist/css/app.css" />
        <!-- END: CSS Assets-->
    </head>
    <!-- END: Head -->
    <body>
        <div class="container grid grid-cols-12 -my-3 min-h-screen relative z-10">
            <div class="col-span-3 bg-gray-800 py-10">
                <a href="" class="intro-x flex items-center pl-5 mb-8">
                    <img alt="Midone Tailwind HTML Admin Template" class="w-6" src="dist/images/logo.svg">
                    <span class="hidden xl:block text-2xl ml-3"> Mid<span class="font-medium">one</span> Docs </span>
                </a>
                <a href="index.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out font-medium bg-gray-900">Getting Started</a> <a href="file-structure.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">File Structure</a> <a href="installation.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Installation</a> <a href="style-customization.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Style Customization</a> <a href="javascript.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Javascript</a> <a href="attributions.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Attributions</a> <a href="support.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Support</a> <a href="changelog.html" class="intro-x flex items-center px-5 py-3 rounded-l hover:bg-gray-900 transition duration-300 ease-in-out">Changelog</a> 
            </div>
            <div class="col-span-9 border-l border-gray-800 bg-gray-900 py-10 px-10">
                <h1 class="intro-y text-2xl font-medium pb-8 mb-10 border-b border-gray-800">
                    Getting Started
                </h1>
                <h2 class="intro-y text-xl font-medium pb-5 mb-5 border-b border-gray-800">
                    Introduction
                </h2>
                <div class="intro-y leading-relaxed">
                    <p class="mb-3">Midone gives you everything you need to create your Admin Dashboard. There are a few things like colors, components, and layouts that carefully chosen and designed for every single page. The template are made with TailwindCSS framework which has many usefull utility classes and great SASS support.</p>
                    <p>The download files will contains 3 directories, the project source files and documentation to help you starting to use this template. It is created to save your time so you can take care the other parts. Every components are completely modular and written with SASS.</p>
                    <p>Thank you for purchasing this template. Congratulations on using Midone, Left4code Team.</p>
                </div>
                <h2 class="intro-y text-xl font-medium pb-5 mb-5 border-b border-gray-800 mt-10">
                    Features
                </h2>
                <div class="w-full grid grid-cols-12 gap-5">
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="airplay" class="mr-3"></i> 
                            <div class="font-medium mt-3">Fully Responsive</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="tool" class="mr-3"></i> 
                            <div class="font-medium mt-3">Built-in Tools</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="sidebar" class="mr-3"></i> 
                            <div class="font-medium mt-3">Functional Dashboard</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="package" class="mr-3"></i> 
                            <div class="font-medium mt-3">Apps Preview</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="users" class="mr-3"></i> 
                            <div class="font-medium mt-3">Users Preview</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="inbox" class="mr-3"></i> 
                            <div class="font-medium mt-3">Easy to Customize</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="file-text" class="mr-3"></i> 
                            <div class="font-medium mt-3">Useful Pages</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="fast-forward" class="mr-3"></i> 
                            <div class="font-medium mt-3">Fast Performance</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="box" class="mr-3"></i> 
                            <div class="font-medium mt-3">Utility Based</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="code" class="mr-3"></i> 
                            <div class="font-medium mt-3">Clean Code & Structure</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="bold" class="mr-3"></i> 
                            <div class="font-medium mt-3">Coded with SASS</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <i data-feather="clock" class="mr-3"></i> 
                            <div class="font-medium mt-3">Free Lifetime Updates</div>
                        </div>
                    </div>
                </div>
                <h2 class="intro-y text-xl font-medium pb-5 mb-5 border-b border-gray-800 mt-10">
                    Browser Support
                </h2>
                <div class="w-full grid grid-cols-12 gap-5">
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <img class="w-10" alt="Browser" src="dist/images/chrome.png">
                            <div class="font-medium mt-3">Chrome</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <img class="w-10" alt="Browser" src="dist/images/edge.png">
                            <div class="font-medium mt-3">Edge</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <img class="w-10" alt="Browser" src="dist/images/mozilla.png">
                            <div class="font-medium mt-3">Mozilla</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <img class="w-10" alt="Browser" src="dist/images/opera.png">
                            <div class="font-medium mt-3">Opera</div>
                        </div>
                    </div>
                    <div class="intro-y col-span-6 sm:col-span-3 cursor-pointer">
                        <div class="zoom-in flex flex-col items-center p-5 rounded-md bg-gray-800">
                            <img class="w-10" alt="Browser" src="dist/images/safari.png">
                            <div class="font-medium mt-3">Safari</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- BEGIN: JS Assets-->
        <script src="dist/js/app.js"></script>
        <!-- END: JS Assets-->
    </body>
</html>