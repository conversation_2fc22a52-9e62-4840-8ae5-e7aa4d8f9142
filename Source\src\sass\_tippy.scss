.tooltip-content {
    left: 10000px;
    @apply fixed;
}
.tippy-popper {
    &[x-placement="left"],
    &[x-placement="right"],
    &[x-placement="left-start"],
    &[x-placement="left-end"],
    &[x-placement="right-start"],
    &[x-placement="right-end"] {
        .tippy-roundarrow,
        .tippy-arrow {
            @apply top-0 bottom-0 my-auto;
        }
    }
    &[x-placement="top"],
    &[x-placement="bottom"],
    &[x-placement="top-start"],
    &[x-placement="top-end"],
    &[x-placement="bottom-start"],
    &[x-placement="bottom-end"] {
        .tippy-roundarrow,
        .tippy-arrow {
            @apply left-0 right-0 mx-auto;
        }
    }
}

.dark {
    .tippy-box {
        background: #1a2231;
        @apply text-gray-300 shadow-none;
        &[data-placement^=top] > .tippy-svg-arrow:after, 
        &[data-placement^=top] > .tippy-svg-arrow > svg {
            fill: #1a2231;
        }
    }
}