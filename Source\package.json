{"name": "MidOne", "version": "2.0.2", "description": "Tailwind HTML Admin Template", "main": "index.js", "author": "Left4code", "private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "dependencies": {"@ckeditor/ckeditor5-basic-styles": "^21.0.0", "@ckeditor/ckeditor5-dev-utils": "^23.1.1", "@ckeditor/ckeditor5-dev-webpack-plugin": "^23.1.1", "@ckeditor/ckeditor5-easy-image": "^21.0.0", "@ckeditor/ckeditor5-editor-balloon": "^21.0.0", "@ckeditor/ckeditor5-editor-classic": "^21.0.0", "@ckeditor/ckeditor5-editor-decoupled": "^21.0.0", "@ckeditor/ckeditor5-editor-inline": "^21.0.0", "@ckeditor/ckeditor5-essentials": "^21.0.0", "@ckeditor/ckeditor5-font": "^21.0.0", "@ckeditor/ckeditor5-heading": "^21.0.0", "@ckeditor/ckeditor5-highlight": "^21.0.0", "@ckeditor/ckeditor5-link": "^21.0.0", "@ckeditor/ckeditor5-paragraph": "^21.0.0", "@ckeditor/ckeditor5-theme-lark": "^21.0.0", "@popperjs/core": "^2.4.4", "cash-dom": "^8.0.0", "chart.js": "^2.9.3", "cross-env": "^7.0.0", "dayjs": "^1.8.33", "dropzone": "^5.7.0", "feather-icons": "^4.26.0", "highlight.js": "^9.18.1", "js-beautify": "^1.11.0", "laravel-mix": "^5.0.1", "litepicker": "^1.5.7", "postcss-loader": "3", "pristinejs": "^0.1.9", "raw-loader": "3", "style-loader": "1", "svg-loaders": "^0.2.0", "tabulator-tables": "^4.7.2", "tail.select": "^0.5.15", "tailwindcss": "^1.4.0", "tiny-slider": "^2.9.3", "tippy.js": "^6.2.6", "toastify-js": "^1.9.0", "velocity-animate": "^1.5.2", "vue-template-compiler": "^2.6.11", "xlsx": "^0.16.6", "zoom-vanilla.js": "^2.0.6"}, "devDependencies": {"axios": "^0.19.2", "browser-sync": "^2.26.7", "browser-sync-webpack-plugin": "^2.0.1", "cross-env": "^7.0", "laravel-mix": "^5.0.1", "lodash": "^4.17.13", "resolve-url-loader": "^3.1.0", "sass": "^1.15.2", "sass-loader": "^8.0.0"}}