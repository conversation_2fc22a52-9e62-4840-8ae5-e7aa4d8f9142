.login {
    @apply relative bg-white;
    @media (max-width: $xl) {
        background: linear-gradient(to bottom, #1C3FAA, #2B51B4);
        background-repeat: no-repeat;
        background-attachment: fixed;
    }
    &:before {
        content: "";
        margin-left: -48%;
        background-image: url(../images/bg-login-page.svg);
        background-repeat: no-repeat;
        background-size: auto 100%;
        background-position: right;
        @apply w-full h-full absolute top-0 left-0;
        @media (max-width: $xl) {
            @apply hidden;
        }
    }
    .login__input {
        min-width: 350px;
        box-shadow: 0px 3px 5px #00000007;
        @media (max-width: $xl) {
            @apply min-w-full;
        }
    }
}

.dark {
    .login {
        @apply bg-dark-1;
        &:before {
            background-image: url(../images/bg-login-page-dark.svg);
        }
    }
}